<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\admin\logic\ParameterSessionLogic;
use think\Db;

// 检查ApiBase是否存在，如果不存在则创建一个简单的基类
if (!class_exists('app\api\controller\ApiBase')) {
    class ApiBase {
        public $user_id = 1001;
        public $request;

        public function success($msg = '', $data = []) {
            return ['code' => 1, 'msg' => $msg, 'data' => $data];
        }

        public function fail($msg = '', $data = []) {
            return ['code' => 0, 'msg' => $msg, 'data' => $data];
        }
    }
}

class ParameterSession extends ApiBase
{
    public $like_not_need_login = [];

    /**
     * 创建参数填写会话
     */
    public function create()
    {
        $goods_id = $this->request->post('goods_id');
        
        if (empty($goods_id)) {
            return $this->fail('商品ID不能为空');
        }
        
        $result = ParameterSessionLogic::createSession($this->user_id, $goods_id);
        
        if ($result['success']) {
            return $this->success('会话创建成功', $result['data']);
        } else {
            return $this->fail($result['error']);
        }
    }

    /**
     * 更新参数值
     */
    public function update()
    {
        $session_id = $this->request->param('session_id');
        $parameter_values = $this->request->post('parameter_values');
        
        if (empty($session_id)) {
            return $this->fail('会话ID不能为空');
        }
        
        if (empty($parameter_values) || !is_array($parameter_values)) {
            return $this->fail('参数值不能为空');
        }
        
        $result = ParameterSessionLogic::updateParameters($session_id, $this->user_id, $parameter_values);
        
        if ($result['success']) {
            return $this->success('参数更新成功', $result['data']);
        } else {
            return $this->fail($result['error']);
        }
    }

    /**
     * 获取会话详情
     */
    public function detail()
    {
        $session_id = $this->request->param('session_id');
        
        if (empty($session_id)) {
            return $this->fail('会话ID不能为空');
        }
        
        $result = ParameterSessionLogic::getSessionDetail($session_id, $this->user_id);
        
        if ($result['success']) {
            return $this->success('获取成功', $result['data']);
        } else {
            return $this->fail($result['error']);
        }
    }

    /**
     * 生成预览
     */
    public function preview()
    {
        $session_id = $this->request->param('session_id');
        $preview_options = $this->request->post('preview_options', []);
        
        if (empty($session_id)) {
            return $this->fail('会话ID不能为空');
        }
        
        // 获取会话详情
        $sessionResult = ParameterSessionLogic::getSessionDetail($session_id, $this->user_id);
        if (!$sessionResult['success']) {
            return $this->fail($sessionResult['error']);
        }
        
        $sessionData = $sessionResult['data'];
        
        // 检查参数完整性
        $parameterValues = $sessionData['parameter_values'] ?? [];
        $templateConfig = $sessionData['template_config'] ?? [];
        $parameterSchema = $templateConfig['parameter_schema'] ?? [];
        
        $missingRequired = [];
        foreach ($parameterSchema as $key => $rules) {
            if (isset($rules['required']) && $rules['required']) {
                if (empty($parameterValues[$key])) {
                    $missingRequired[] = $key;
                }
            }
        }
        
        if (!empty($missingRequired)) {
            return $this->fail('参数不完整，无法生成预览', [
                'missing_required_parameters' => $missingRequired,
                'completion_rate' => $sessionData['completion_rate'] ?? 0,
                'can_preview' => false
            ]);
        }
        
        try {
            // 调用海报模板服务生成预览
            $posterService = new PosterTemplateService();
            $previewResult = $posterService->generatePreview(
                $templateConfig['id'],
                $parameterValues,
                $preview_options
            );
            
            if ($previewResult && isset($previewResult['preview_url'])) {
                return $this->success('预览生成成功', [
                    'preview_url' => $previewResult['preview_url'],
                    'preview_expires_at' => date('Y-m-d H:i:s', time() + 2*3600), // 2小时后过期
                    'preview_id' => $previewResult['preview_id'] ?? uniqid('preview_'),
                    'generation_time' => $previewResult['generation_time'] ?? 0
                ]);
            } else {
                return $this->fail('预览生成失败，请稍后重试');
            }
            
        } catch (\Exception $e) {
            return $this->fail('预览生成异常：' . $e->getMessage());
        }
    }

    /**
     * 完成填写并创建订单
     */
    public function complete()
    {
        $session_id = $this->request->param('session_id');
        $order_info = $this->request->post('order_info');
        
        if (empty($session_id)) {
            return $this->fail('会话ID不能为空');
        }
        
        if (empty($order_info) || !is_array($order_info)) {
            return $this->fail('订单信息不能为空');
        }
        
        // 验证必要的订单信息
        $required_fields = ['consignee', 'mobile', 'address'];
        foreach ($required_fields as $field) {
            if (empty($order_info[$field])) {
                return $this->fail("订单信息缺少必要字段：{$field}");
            }
        }
        
        // 获取会话详情
        $sessionResult = ParameterSessionLogic::getSessionDetail($session_id, $this->user_id);
        if (!$sessionResult['success']) {
            return $this->fail($sessionResult['error']);
        }
        
        $sessionData = $sessionResult['data'];
        
        // 检查是否可以提交
        if ($sessionData['completion_rate'] < 100) {
            return $this->fail('参数填写未完成，无法创建订单');
        }
        
        Db::startTrans();
        try {
            // 创建订单
            $orderData = [
                'user_id' => $this->user_id,
                'order_sn' => $this->generateOrderSn(),
                'order_type' => 2, // 服务订单
                'goods_id' => $sessionData['goods_info']['id'],
                'goods_name' => $sessionData['goods_info']['name'],
                'goods_price' => $sessionData['goods_info']['price'],
                'total_amount' => $sessionData['goods_info']['price'],
                'consignee' => $order_info['consignee'],
                'mobile' => $order_info['mobile'],
                'address' => $order_info['address'],
                'remark' => $order_info['remark'] ?? '',
                'order_status' => 0, // 待支付
                'pay_status' => 0, // 未支付
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $order_id = Db::name('order')->insertGetId($orderData);
            
            // 创建订单参数记录
            $parameterRecord = [
                'order_id' => $order_id,
                'session_id' => $session_id,
                'template_config_id' => $sessionData['template_config']['id'],
                'parameter_values' => json_encode($sessionData['parameter_values']),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('order_parameters')->insert($parameterRecord);
            
            // 创建服务交付记录
            $deliveryRecord = [
                'order_id' => $order_id,
                'user_id' => $this->user_id,
                'goods_id' => $sessionData['goods_info']['id'],
                'template_config_id' => $sessionData['template_config']['id'],
                'parameter_values' => json_encode($sessionData['parameter_values']),
                'status' => 'pending_generation',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('service_delivery_records')->insert($deliveryRecord);
            
            // 标记会话为已完成
            Db::name('parameter_fill_sessions')
                ->where('id', $session_id)
                ->update([
                    'is_completed' => 1,
                    'completed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            Db::commit();
            
            // 生成支付信息
            $payment_method = $order_info['payment_method'] ?? 'wechat';
            $payment_info = $this->generatePaymentInfo($order_id, $orderData['total_amount'], $payment_method);
            
            return $this->success('订单创建成功', [
                'order_id' => $order_id,
                'order_sn' => $orderData['order_sn'],
                'total_amount' => $orderData['total_amount'],
                'payment_info' => $payment_info,
                'delivery_info' => [
                    'estimated_delivery_time' => '3-5个工作日',
                    'delivery_address' => $order_info['address']
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->fail('订单创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户的参数填写会话列表
     */
    public function lists()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $status = $this->request->get('status', '');
        
        $where = ['user_id' => $this->user_id];
        
        // 状态筛选
        if (!empty($status)) {
            switch ($status) {
                case 'active':
                    $where[] = ['is_completed', '=', 0];
                    $where[] = ['expires_at', '>', date('Y-m-d H:i:s')];
                    break;
                case 'completed':
                    $where[] = ['is_completed', '=', 1];
                    break;
                case 'expired':
                    $where[] = ['is_completed', '=', 0];
                    $where[] = ['expires_at', '<=', date('Y-m-d H:i:s')];
                    break;
            }
        }
        
        $sessions = Db::name('parameter_fill_sessions')
            ->alias('s')
            ->leftJoin('goods g', 's.goods_id = g.id')
            ->where($where)
            ->field('s.*, g.name as goods_name, g.image as goods_image, g.price as goods_price')
            ->order('s.created_at desc')
            ->paginate($limit, false, ['page' => $page]);
        
        $list = $sessions->items();
        
        // 处理数据
        foreach ($list as &$item) {
            // 计算剩余时间
            $remainingSeconds = strtotime($item['expires_at']) - time();
            $item['remaining_hours'] = max(0, round($remainingSeconds / 3600, 1));
            
            // 确定状态
            if ($item['is_completed']) {
                $item['status'] = 'completed';
                $item['status_text'] = '已完成';
            } elseif ($remainingSeconds <= 0) {
                $item['status'] = 'expired';
                $item['status_text'] = '已过期';
            } else {
                $item['status'] = 'active';
                $item['status_text'] = '进行中';
            }
            
            // 解析参数值
            $item['parameter_values'] = json_decode($item['parameter_values'], true) ?: [];
        }
        
        return $this->success('获取成功', [
            'list' => $list,
            'total' => $sessions->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 生成订单号
     */
    private function generateOrderSn()
    {
        return 'LS' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 生成支付信息
     */
    private function generatePaymentInfo($order_id, $amount, $payment_method)
    {
        // 这里应该调用实际的支付服务
        // 暂时返回模拟数据
        return [
            'payment_method' => $payment_method,
            'payment_url' => 'weixin://wxpay/bizpayurl?pr=' . base64_encode($order_id),
            'expires_at' => date('Y-m-d H:i:s', time() + 30*60) // 30分钟后过期
        ];
    }
}
