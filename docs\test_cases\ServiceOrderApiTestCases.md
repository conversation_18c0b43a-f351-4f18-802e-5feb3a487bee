# 服务订单API接口测试用例

## 测试目标
验证移动端服务订单API接口的完整功能，包括订单创建、状态查询、交付跟踪等核心业务流程的正确性和稳定性。

## 测试环境要求
- API服务正常运行
- 参数填写会话功能已实现
- 服务订单逻辑类已实现
- 数据库中存在测试用的商品和用户数据
- 支付和物流系统正常工作
- 用户认证系统正常工作

## 测试用例

### TC001: 创建服务订单 - 正常流程
**测试目的**: 验证用户创建服务订单的正常流程
**接口路径**: `POST /api/service_order/create`
**前置条件**: 
- 用户已登录
- 用户已完成参数填写会话
- 商品为有效的服务商品
- 用户有有效的收货地址

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "address_id": 123,
  "print_options": {
    "material": "premium_paper",
    "size": "A4",
    "quantity": 1
  },
  "payment_method": "wechat",
  "remark": "请尽快处理",
  "headers": {
    "Authorization": "Bearer valid_token",
    "Content-Type": "application/json"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "订单创建成功",
  "data": {
    "order_id": 12345,
    "order_sn": "SO202401150001",
    "total_amount": 29.90,
    "goods_info": {
      "id": 456,
      "name": "海报定制服务",
      "image": "/uploads/goods/poster.jpg"
    },
    "delivery_info": {
      "consignee": "张三",
      "mobile": "13800138000",
      "address": "北京市朝阳区xxx街道xxx号",
      "estimated_delivery_time": "3-5个工作日"
    },
    "payment_info": {
      "payment_method": "wechat",
      "payment_url": "weixin://wxpay/...",
      "expires_at": "2024-01-15 11:00:00"
    },
    "service_info": {
      "template_name": "商务海报模板",
      "parameter_values": {
        "title": "我的专属海报",
        "subtitle": "专业定制服务"
      },
      "print_options": {
        "material": "premium_paper",
        "size": "A4",
        "quantity": 1
      }
    }
  }
}
```

**执行步骤**:
1. 发送POST请求到创建订单接口
2. 验证响应状态码为200
3. 验证响应数据结构正确
4. 验证订单号格式正确
5. 验证支付信息生成正确

**预期结果**:
- 响应状态码为200
- 返回有效的订单ID和订单号
- 商品信息和配送信息完整
- 支付信息正确生成
- 数据库中正确创建订单记录

### TC002: 创建服务订单 - 参数会话不存在
**测试目的**: 验证参数会话不存在时的错误处理
**接口路径**: `POST /api/service_order/create`
**前置条件**: 
- 用户已登录
- 提供的参数会话ID不存在

**测试数据**:
```json
{
  "session_id": "invalid_session_id",
  "address_id": 123,
  "print_options": {
    "material": "premium_paper",
    "size": "A4",
    "quantity": 1
  }
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "参数填写会话不存在或已过期",
  "data": null
}
```

**执行步骤**:
1. 发送包含无效会话ID的请求
2. 验证返回错误响应
3. 验证错误信息准确
4. 验证不创建任何订单记录

**预期结果**:
- 返回错误响应
- 错误信息明确指出会话问题
- 不创建任何数据库记录
- 不扣减用户余额

### TC003: 获取服务订单状态 - 正常流程
**测试目的**: 验证获取服务订单状态的功能
**接口路径**: `GET /api/service_order/{order_id}/status`
**前置条件**: 
- 用户已登录
- 存在有效的服务订单

**测试数据**:
```json
{
  "order_id": 12345,
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "order_id": 12345,
    "order_sn": "SO202401150001",
    "order_status": "paid",
    "order_status_text": "已支付",
    "pay_status": "paid",
    "pay_status_text": "已支付",
    "delivery_status": "generating",
    "delivery_status_text": "生成中",
    "created_at": "2024-01-15 10:30:00",
    "paid_at": "2024-01-15 10:35:00",
    "estimated_delivery_time": "2024-01-18 18:00:00",
    "progress": {
      "current_step": 2,
      "total_steps": 5,
      "steps": [
        {"step": 1, "name": "订单确认", "status": "completed", "time": "2024-01-15 10:30:00"},
        {"step": 2, "name": "支付完成", "status": "completed", "time": "2024-01-15 10:35:00"},
        {"step": 3, "name": "海报生成", "status": "processing", "time": null},
        {"step": 4, "name": "打印制作", "status": "pending", "time": null},
        {"step": 5, "name": "物流配送", "status": "pending", "time": null}
      ]
    }
  }
}
```

**执行步骤**:
1. 发送GET请求获取订单状态
2. 验证返回数据完整性
3. 验证状态信息准确性
4. 验证进度信息正确

**预期结果**:
- 返回完整的订单状态信息
- 订单状态和支付状态准确
- 交付进度信息详细
- 时间信息格式正确

### TC004: 获取交付信息 - 正常流程
**测试目的**: 验证获取服务订单交付信息的功能
**接口路径**: `GET /api/service_order/{order_id}/delivery`
**前置条件**: 
- 用户已登录
- 存在有效的服务订单
- 订单已开始交付流程

**测试数据**:
```json
{
  "order_id": 12345,
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "order_id": 12345,
    "delivery_status": "shipped",
    "delivery_status_text": "已发货",
    "poster_info": {
      "preview_url": "https://example.com/preview/poster_12345.jpg",
      "download_url": "https://example.com/download/poster_12345.pdf",
      "generated_at": "2024-01-16 14:30:00",
      "file_size": "2.5MB",
      "resolution": "300DPI"
    },
    "print_info": {
      "material": "premium_paper",
      "size": "A4",
      "quantity": 1,
      "print_quality": "excellent",
      "printed_at": "2024-01-17 09:15:00"
    },
    "shipping_info": {
      "express_company": "顺丰速运",
      "tracking_number": "SF1234567890",
      "shipped_at": "2024-01-17 16:20:00",
      "estimated_arrival": "2024-01-19 18:00:00",
      "current_location": "北京分拣中心",
      "delivery_address": "北京市朝阳区xxx街道xxx号"
    },
    "timeline": [
      {"time": "2024-01-15 10:30:00", "status": "订单创建", "description": "订单创建成功"},
      {"time": "2024-01-15 10:35:00", "status": "支付完成", "description": "微信支付成功"},
      {"time": "2024-01-16 14:30:00", "status": "海报生成", "description": "海报生成完成"},
      {"time": "2024-01-17 09:15:00", "status": "打印完成", "description": "海报打印完成"},
      {"time": "2024-01-17 16:20:00", "status": "已发货", "description": "顺丰快递已揽收"}
    ]
  }
}
```

**执行步骤**:
1. 发送GET请求获取交付信息
2. 验证海报信息完整
3. 验证打印信息准确
4. 验证物流信息详细

**预期结果**:
- 返回完整的交付信息
- 海报预览和下载链接有效
- 打印信息详细准确
- 物流跟踪信息实时更新

### TC005: 下载生成文件
**测试目的**: 验证用户下载生成海报文件的功能
**接口路径**: `POST /api/service_order/{order_id}/download`
**前置条件**: 
- 用户已登录
- 订单海报已生成完成
- 用户有下载权限

**测试数据**:
```json
{
  "order_id": 12345,
  "file_type": "pdf",
  "quality": "high",
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "下载链接生成成功",
  "data": {
    "download_url": "https://example.com/download/poster_12345_high.pdf",
    "file_name": "我的专属海报_高清版.pdf",
    "file_size": "2.5MB",
    "expires_at": "2024-01-15 12:00:00",
    "download_token": "download_token_abc123"
  }
}
```

**执行步骤**:
1. 发送POST请求生成下载链接
2. 验证下载链接有效
3. 验证文件信息正确
4. 验证下载权限控制

**预期结果**:
- 成功生成下载链接
- 文件信息准确完整
- 下载链接有时效性
- 权限控制严格

### TC006: 获取用户服务订单列表
**测试目的**: 验证获取用户服务订单列表的功能
**接口路径**: `GET /api/service_order/lists`
**前置条件**: 
- 用户已登录
- 用户有服务订单记录

**测试数据**:
```json
{
  "page": 1,
  "limit": 10,
  "status": "all",
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "order_id": 12345,
        "order_sn": "SO202401150001",
        "goods_name": "海报定制服务",
        "goods_image": "/uploads/goods/poster.jpg",
        "total_amount": 29.90,
        "order_status": "completed",
        "order_status_text": "已完成",
        "delivery_status": "delivered",
        "delivery_status_text": "已送达",
        "created_at": "2024-01-15 10:30:00",
        "estimated_delivery_time": "2024-01-19 18:00:00",
        "can_download": true,
        "can_cancel": false
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10,
    "has_more": false
  }
}
```

**执行步骤**:
1. 发送GET请求获取订单列表
2. 验证列表数据完整
3. 验证分页信息正确
4. 验证操作权限准确

**预期结果**:
- 返回用户的服务订单列表
- 订单信息完整准确
- 分页功能正常
- 操作权限控制正确

### TC007: 取消服务订单
**测试目的**: 验证取消服务订单的功能
**接口路径**: `POST /api/service_order/{order_id}/cancel`
**前置条件**: 
- 用户已登录
- 订单状态允许取消
- 订单属于当前用户

**测试数据**:
```json
{
  "order_id": 12345,
  "cancel_reason": "不需要了",
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "订单取消成功",
  "data": {
    "order_id": 12345,
    "cancel_time": "2024-01-15 11:00:00",
    "refund_info": {
      "refund_amount": 29.90,
      "refund_method": "原路退回",
      "estimated_refund_time": "1-3个工作日"
    }
  }
}
```

**执行步骤**:
1. 发送POST请求取消订单
2. 验证取消成功
3. 验证退款信息正确
4. 验证订单状态更新

**预期结果**:
- 订单成功取消
- 退款信息准确
- 订单状态正确更新
- 相关记录正确处理

### TC008: 权限验证 - 无效token
**测试目的**: 验证API接口的权限验证功能
**接口路径**: `POST /api/service_order/create`
**前置条件**: 
- 使用无效或过期的token

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "address_id": 123,
  "headers": {
    "Authorization": "Bearer invalid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "用户未登录或token已过期",
  "data": null
}
```

**执行步骤**:
1. 使用无效token发送请求
2. 验证返回权限错误
3. 验证错误信息准确
4. 验证不执行业务逻辑

**预期结果**:
- 返回权限验证失败
- 错误信息明确
- 不执行任何业务操作
- 响应状态码为401

### TC009: 订单状态流转验证
**测试目的**: 验证服务订单状态的正确流转
**接口路径**: `GET /api/service_order/{order_id}/status`
**前置条件**: 
- 用户已登录
- 订单处于不同状态

**测试数据**:
```json
{
  "order_states": [
    {"order_id": 12345, "expected_status": "unpaid"},
    {"order_id": 12346, "expected_status": "paid"},
    {"order_id": 12347, "expected_status": "generating"},
    {"order_id": 12348, "expected_status": "printing"},
    {"order_id": 12349, "expected_status": "shipped"},
    {"order_id": 12350, "expected_status": "completed"}
  ]
}
```

**执行步骤**:
1. 分别查询不同状态的订单
2. 验证状态显示正确
3. 验证状态流转逻辑
4. 验证操作权限控制

**预期结果**:
- 各状态订单显示正确
- 状态流转逻辑合理
- 操作权限控制准确
- 状态文本描述清晰

### TC010: 异常情况处理
**测试目的**: 验证各种异常情况的处理
**接口路径**: 多个接口
**前置条件**: 
- 模拟各种异常情况

**测试场景**:
1. **网络异常**: 模拟网络超时
2. **服务异常**: 模拟后端服务不可用
3. **数据异常**: 模拟数据库连接失败
4. **业务异常**: 模拟库存不足、支付失败等

**执行步骤**:
1. 模拟各种异常情况
2. 发送API请求
3. 验证错误处理机制
4. 验证用户友好的错误提示

**预期结果**:
- 异常情况得到正确处理
- 错误信息用户友好
- 系统状态保持一致
- 不影响其他功能

## 测试方法说明

### API测试验证方法
1. **接口功能测试**: 验证每个API接口的基本功能和业务逻辑
2. **参数验证测试**: 测试各种有效和无效的参数组合
3. **权限验证测试**: 测试用户身份验证和权限控制
4. **异常场景测试**: 测试网络异常、服务异常等边界情况

### 集成测试验证方法
1. **端到端流程测试**: 从订单创建到交付完成的完整流程
2. **服务依赖测试**: 验证与支付、物流等外部服务的集成
3. **数据一致性测试**: 验证数据库操作的一致性和完整性

### 性能测试验证方法
1. **响应时间测试**: 验证API接口的响应时间
2. **并发测试**: 测试高并发情况下的系统稳定性
3. **负载测试**: 验证系统在预期负载下的性能表现

### 测试执行顺序
1. 先测试基础接口功能（TC001、TC003、TC006）
2. 再测试核心业务流程（TC004、TC005、TC007）
3. 然后测试异常和边界情况（TC002、TC008、TC009、TC010）

## 成功标准
- 所有API接口功能正常
- 订单创建和状态管理准确
- 交付信息跟踪完整
- 权限控制严格有效
- 异常处理机制可靠
- 数据一致性得到保证
- 性能满足预期要求
- 与现有系统集成无冲突

## 测试数据准备
### 测试用户数据
- 用户ID: 1001
- 用户昵称: 测试用户
- 手机号: 13800138000

### 测试商品数据
- 商品ID: 456
- 商品名称: 海报定制服务
- 商品类型: 2 (服务商品)
- 商品价格: 29.90

### 测试地址数据
- 地址ID: 123
- 收货人: 张三
- 手机号: 13800138000
- 地址: 北京市朝阳区xxx街道xxx号

### 测试会话数据
- 会话ID: session_20240115_001
- 模板ID: template_001
- 参数值: {"title": "我的专属海报", "subtitle": "专业定制服务"}
