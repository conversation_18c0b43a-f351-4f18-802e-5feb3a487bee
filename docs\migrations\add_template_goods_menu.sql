-- 模板商品化系统完整菜单数据插入
-- 创建时间: 2024-01-15
-- 描述: 为模板商品化系统添加完整的后台管理菜单

SET NAMES utf8mb4;

-- 获取当前最大的sort值，确保新菜单排在合适位置
SET @max_sort = (SELECT IFNULL(MAX(sort), 0) FROM ls_dev_auth WHERE pid = 0);

-- ============================================================================
-- 1. 添加主菜单：模板商品化
-- ============================================================================
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(0, 1, 0, '模板商品化', 'layui-icon-template', @max_sort + 20, '', 0, 0, UNIX_TIMESTAMP());

-- 获取刚插入的主菜单ID
SET @main_menu_id = LAST_INSERT_ID();

-- ============================================================================
-- 2. 添加子菜单
-- ============================================================================

-- 2.1 团购活动管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '团购活动', 'layui-icon-group', 100, 'group_buy/lists', 0, 0, UNIX_TIMESTAMP());

SET @group_buy_menu_id = LAST_INSERT_ID();

-- 2.2 参数填写会话管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '参数会话', 'layui-icon-form', 90, 'parameter_session/lists', 0, 0, UNIX_TIMESTAMP());

SET @parameter_session_menu_id = LAST_INSERT_ID();

-- 2.3 服务交付管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '服务交付', 'layui-icon-delivery', 80, 'service_delivery/lists', 0, 0, UNIX_TIMESTAMP());

SET @service_delivery_menu_id = LAST_INSERT_ID();

-- 2.4 模板订单参数
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '订单参数', 'layui-icon-list', 70, 'template_order_params/lists', 0, 0, UNIX_TIMESTAMP());

SET @order_params_menu_id = LAST_INSERT_ID();

-- 2.5 系统统计
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '系统统计', 'layui-icon-chart-screen', 60, 'template_goods/statistics', 0, 0, UNIX_TIMESTAMP());

SET @statistics_menu_id = LAST_INSERT_ID();

-- ============================================================================
-- 3. 添加权限节点（type=2表示权限）
-- ============================================================================

-- 3.1 团购活动相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@group_buy_menu_id, 2, 0, '查看团购列表', '', 100, 'group_buy/lists', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '添加团购活动', '', 90, 'group_buy/add', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '编辑团购活动', '', 80, 'group_buy/edit', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '删除团购活动', '', 70, 'group_buy/delete', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '查看团购详情', '', 60, 'group_buy/detail', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '团购状态管理', '', 50, 'group_buy/status', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '团购参与者管理', '', 40, 'group_buy/participants', 0, 0, UNIX_TIMESTAMP()),
(@group_buy_menu_id, 2, 0, '批量处理团购', '', 30, 'group_buy/batch_process', 0, 0, UNIX_TIMESTAMP());

-- 3.2 参数填写会话相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@parameter_session_menu_id, 2, 0, '查看会话列表', '', 100, 'parameter_session/lists', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '查看会话详情', '', 90, 'parameter_session/detail', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '编辑会话参数', '', 80, 'parameter_session/edit', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '删除会话', '', 70, 'parameter_session/delete', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '生成预览', '', 60, 'parameter_session/preview', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '完成会话', '', 50, 'parameter_session/complete', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '批量处理会话', '', 40, 'parameter_session/batch_process', 0, 0, UNIX_TIMESTAMP()),
(@parameter_session_menu_id, 2, 0, '清理过期会话', '', 30, 'parameter_session/cleanup_expired', 0, 0, UNIX_TIMESTAMP());

-- 3.3 服务交付相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@service_delivery_menu_id, 2, 0, '查看交付列表', '', 100, 'service_delivery/lists', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '查看交付详情', '', 90, 'service_delivery/detail', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '编辑交付信息', '', 80, 'service_delivery/edit', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '删除交付记录', '', 70, 'service_delivery/delete', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '更新交付状态', '', 60, 'service_delivery/update_status', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '批量生成海报', '', 50, 'service_delivery/batch_generate', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '更新物流信息', '', 40, 'service_delivery/update_shipping', 0, 0, UNIX_TIMESTAMP()),
(@service_delivery_menu_id, 2, 0, '导出交付数据', '', 30, 'service_delivery/export', 0, 0, UNIX_TIMESTAMP());

-- 3.4 订单参数相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@order_params_menu_id, 2, 0, '查看订单参数', '', 100, 'template_order_params/lists', 0, 0, UNIX_TIMESTAMP()),
(@order_params_menu_id, 2, 0, '查看参数详情', '', 90, 'template_order_params/detail', 0, 0, UNIX_TIMESTAMP()),
(@order_params_menu_id, 2, 0, '编辑订单参数', '', 80, 'template_order_params/edit', 0, 0, UNIX_TIMESTAMP()),
(@order_params_menu_id, 2, 0, '删除订单参数', '', 70, 'template_order_params/delete', 0, 0, UNIX_TIMESTAMP()),
(@order_params_menu_id, 2, 0, '导出参数数据', '', 60, 'template_order_params/export', 0, 0, UNIX_TIMESTAMP());

-- 3.5 系统统计相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@statistics_menu_id, 2, 0, '查看统计数据', '', 100, 'template_goods/statistics', 0, 0, UNIX_TIMESTAMP()),
(@statistics_menu_id, 2, 0, '导出统计报告', '', 90, 'template_goods/export_report', 0, 0, UNIX_TIMESTAMP()),
(@statistics_menu_id, 2, 0, '系统健康检查', '', 80, 'template_goods/health_check', 0, 0, UNIX_TIMESTAMP());

-- ============================================================================
-- 4. 扩展现有商品管理菜单权限
-- ============================================================================

-- 获取商品管理菜单ID（假设存在）
SET @goods_menu_id = (SELECT id FROM ls_dev_auth WHERE uri LIKE '%goods%' AND type = 1 AND name LIKE '%商品%' LIMIT 1);

-- 如果找到商品管理菜单，添加服务商品相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) 
SELECT @goods_menu_id, 2, 0, '服务商品管理', '', 25, 'goods/service_goods', 0, 0, UNIX_TIMESTAMP()
WHERE @goods_menu_id IS NOT NULL;

INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) 
SELECT @goods_menu_id, 2, 0, '模板配置管理', '', 24, 'goods/template_config', 0, 0, UNIX_TIMESTAMP()
WHERE @goods_menu_id IS NOT NULL;

-- ============================================================================
-- 5. 扩展现有订单管理菜单权限
-- ============================================================================

-- 获取订单管理菜单ID（假设存在）
SET @order_menu_id = (SELECT id FROM ls_dev_auth WHERE uri LIKE '%order%' AND type = 1 AND name LIKE '%订单%' LIMIT 1);

-- 如果找到订单管理菜单，添加服务订单相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) 
SELECT @order_menu_id, 2, 0, '服务订单管理', '', 25, 'order/service_orders', 0, 0, UNIX_TIMESTAMP()
WHERE @order_menu_id IS NOT NULL;

INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) 
SELECT @order_menu_id, 2, 0, '订单交付管理', '', 24, 'order/delivery_management', 0, 0, UNIX_TIMESTAMP()
WHERE @order_menu_id IS NOT NULL;

-- ============================================================================
-- 6. 清除菜单缓存（如果有缓存机制）
-- ============================================================================
-- 注意：这里可能需要根据实际的缓存清理机制来调整

-- ============================================================================
-- 7. 显示插入结果
-- ============================================================================
SELECT 'Template Goods System Menu Added Successfully!' as Status;

-- 显示插入的菜单结构
SELECT 
    id,
    pid,
    type,
    name,
    uri,
    CASE type 
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '权限'
        ELSE '未知'
    END as type_name,
    sort
FROM ls_dev_auth 
WHERE (id = @main_menu_id OR pid = @main_menu_id OR pid IN (
    SELECT id FROM ls_dev_auth WHERE pid = @main_menu_id
))
ORDER BY pid, sort DESC;

-- 显示菜单统计
SELECT 
    COUNT(CASE WHEN type = 1 THEN 1 END) as menu_count,
    COUNT(CASE WHEN type = 2 THEN 1 END) as permission_count,
    COUNT(*) as total_count
FROM ls_dev_auth 
WHERE (id = @main_menu_id OR pid = @main_menu_id OR pid IN (
    SELECT id FROM ls_dev_auth WHERE pid = @main_menu_id
));
