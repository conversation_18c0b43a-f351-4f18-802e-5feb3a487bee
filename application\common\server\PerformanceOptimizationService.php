<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\common\server;

use think\Cache;
use think\Db;
use think\Log;

/**
 * 性能优化服务类
 * 提供缓存管理、查询优化、监控等性能优化功能
 */
class PerformanceOptimizationService
{
    // 缓存键前缀
    const CACHE_PREFIX = 'perf_opt_';
    
    // 缓存过期时间配置
    const CACHE_EXPIRE = [
        'goods_list' => 300,        // 商品列表缓存5分钟
        'group_buy_list' => 180,    // 团购列表缓存3分钟
        'template_config' => 3600,  // 模板配置缓存1小时
        'user_session' => 1800,     // 用户会话缓存30分钟
        'order_status' => 60,       // 订单状态缓存1分钟
    ];
    
    /**
     * 获取缓存的商品列表
     */
    public static function getCachedGoodsList($params = [])
    {
        $cacheKey = self::CACHE_PREFIX . 'goods_list_' . md5(serialize($params));
        
        $data = Cache::get($cacheKey);
        if ($data !== false) {
            return $data;
        }
        
        // 优化的商品查询
        $where = [];
        if (isset($params['goods_type'])) {
            $where['goods_type'] = $params['goods_type'];
        }
        if (isset($params['status'])) {
            $where['status'] = $params['status'];
        }
        
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        
        // 使用优化的查询，利用复合索引
        $goods = Db::name('goods')
            ->where($where)
            ->field('id, name, image, price, goods_type, status, create_time')
            ->order('create_time DESC')
            ->paginate($limit, false, ['page' => $page]);
        
        $result = [
            'list' => $goods->items(),
            'total' => $goods->total(),
            'page' => $page,
            'limit' => $limit
        ];
        
        // 缓存结果
        Cache::set($cacheKey, $result, self::CACHE_EXPIRE['goods_list']);
        
        return $result;
    }
    
    /**
     * 获取缓存的团购活动列表
     */
    public static function getCachedGroupBuyList($params = [])
    {
        $cacheKey = self::CACHE_PREFIX . 'group_buy_list_' . md5(serialize($params));
        
        $data = Cache::get($cacheKey);
        if ($data !== false) {
            return $data;
        }
        
        // 优化的团购查询
        $where = [];
        if (isset($params['status'])) {
            $where['gb.status'] = $params['status'];
        }
        
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        
        // 使用JOIN查询减少数据库访问次数
        $activities = Db::name('group_buy_activities')
            ->alias('gb')
            ->leftJoin('goods g', 'gb.goods_id = g.id')
            ->where($where)
            ->field('gb.*, g.name as goods_name, g.image as goods_image, g.price as original_price')
            ->order('gb.created_at DESC')
            ->paginate($limit, false, ['page' => $page]);
        
        $result = [
            'list' => $activities->items(),
            'total' => $activities->total(),
            'page' => $page,
            'limit' => $limit
        ];
        
        // 缓存结果
        Cache::set($cacheKey, $result, self::CACHE_EXPIRE['group_buy_list']);
        
        return $result;
    }
    
    /**
     * 获取缓存的模板配置
     */
    public static function getCachedTemplateConfig($templateId)
    {
        $cacheKey = self::CACHE_PREFIX . 'template_config_' . $templateId;
        
        $data = Cache::get($cacheKey);
        if ($data !== false) {
            return $data;
        }
        
        // 这里应该调用实际的模板服务
        // 暂时返回模拟数据
        $config = [
            'id' => $templateId,
            'name' => '商务海报模板',
            'parameter_schema' => [
                'title' => ['type' => 'text', 'required' => true, 'max_length' => 50],
                'subtitle' => ['type' => 'text', 'required' => false, 'max_length' => 100],
                'background_color' => ['type' => 'color', 'required' => false, 'default' => '#ffffff']
            ]
        ];
        
        // 长时间缓存模板配置
        Cache::set($cacheKey, $config, self::CACHE_EXPIRE['template_config']);
        
        return $config;
    }
    
    /**
     * 批量获取订单状态（减少数据库查询次数）
     */
    public static function getBatchOrderStatus($orderIds)
    {
        if (empty($orderIds)) {
            return [];
        }
        
        // 尝试从缓存获取
        $cachedResults = [];
        $uncachedIds = [];
        
        foreach ($orderIds as $orderId) {
            $cacheKey = self::CACHE_PREFIX . 'order_status_' . $orderId;
            $cached = Cache::get($cacheKey);
            if ($cached !== false) {
                $cachedResults[$orderId] = $cached;
            } else {
                $uncachedIds[] = $orderId;
            }
        }
        
        // 批量查询未缓存的订单状态
        $dbResults = [];
        if (!empty($uncachedIds)) {
            $orders = Db::name('order')
                ->whereIn('id', $uncachedIds)
                ->field('id, order_sn, order_status, pay_status, create_time, update_time')
                ->select();
            
            foreach ($orders as $order) {
                $dbResults[$order['id']] = $order;
                
                // 缓存结果
                $cacheKey = self::CACHE_PREFIX . 'order_status_' . $order['id'];
                Cache::set($cacheKey, $order, self::CACHE_EXPIRE['order_status']);
            }
        }
        
        return array_merge($cachedResults, $dbResults);
    }
    
    /**
     * 优化的用户会话查询
     */
    public static function getOptimizedUserSessions($userId, $params = [])
    {
        $cacheKey = self::CACHE_PREFIX . 'user_sessions_' . $userId . '_' . md5(serialize($params));
        
        $data = Cache::get($cacheKey);
        if ($data !== false) {
            return $data;
        }
        
        $where = ['user_id' => $userId];
        if (isset($params['is_completed'])) {
            $where['is_completed'] = $params['is_completed'];
        }
        
        // 使用索引优化的查询
        $sessions = Db::name('parameter_fill_sessions')
            ->alias('s')
            ->leftJoin('goods g', 's.goods_id = g.id')
            ->where($where)
            ->field('s.*, g.name as goods_name, g.image as goods_image')
            ->order('s.created_at DESC')
            ->limit($params['limit'] ?? 10)
            ->select();
        
        // 缓存结果
        Cache::set($cacheKey, $sessions, self::CACHE_EXPIRE['user_session']);
        
        return $sessions;
    }
    
    /**
     * 清除相关缓存
     */
    public static function clearRelatedCache($type, $id = null)
    {
        $patterns = [];
        
        switch ($type) {
            case 'goods':
                $patterns[] = self::CACHE_PREFIX . 'goods_list_*';
                if ($id) {
                    $patterns[] = self::CACHE_PREFIX . 'template_config_*';
                }
                break;
                
            case 'group_buy':
                $patterns[] = self::CACHE_PREFIX . 'group_buy_list_*';
                $patterns[] = self::CACHE_PREFIX . 'goods_list_*';
                break;
                
            case 'order':
                if ($id) {
                    $patterns[] = self::CACHE_PREFIX . 'order_status_' . $id;
                    $patterns[] = self::CACHE_PREFIX . 'user_sessions_*';
                }
                break;
                
            case 'session':
                if ($id) {
                    $patterns[] = self::CACHE_PREFIX . 'user_sessions_*';
                }
                break;
        }
        
        foreach ($patterns as $pattern) {
            self::clearCacheByPattern($pattern);
        }
    }
    
    /**
     * 根据模式清除缓存
     */
    private static function clearCacheByPattern($pattern)
    {
        // 这里需要根据实际的缓存驱动实现
        // Redis可以使用KEYS命令，文件缓存需要遍历文件
        try {
            if (Cache::getConfig('type') === 'redis') {
                $redis = Cache::handler();
                $keys = $redis->keys(str_replace('*', '*', $pattern));
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // 文件缓存的清理逻辑
                Cache::clear();
            }
        } catch (\Exception $e) {
            Log::error('清除缓存失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 数据库查询性能监控
     */
    public static function monitorQueryPerformance($sql, $params = [], $executeTime = 0)
    {
        // 记录慢查询
        if ($executeTime > 1000) { // 超过1秒的查询
            Log::warning('慢查询检测', [
                'sql' => $sql,
                'params' => $params,
                'execute_time' => $executeTime . 'ms',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        
        // 记录查询统计
        $cacheKey = self::CACHE_PREFIX . 'query_stats_' . date('Y-m-d-H');
        $stats = Cache::get($cacheKey) ?: ['total' => 0, 'slow' => 0, 'avg_time' => 0];
        
        $stats['total']++;
        if ($executeTime > 1000) {
            $stats['slow']++;
        }
        $stats['avg_time'] = ($stats['avg_time'] * ($stats['total'] - 1) + $executeTime) / $stats['total'];
        
        Cache::set($cacheKey, $stats, 3600); // 缓存1小时
    }
    
    /**
     * API响应时间监控
     */
    public static function monitorApiPerformance($endpoint, $method, $responseTime, $statusCode)
    {
        $cacheKey = self::CACHE_PREFIX . 'api_stats_' . date('Y-m-d-H');
        $stats = Cache::get($cacheKey) ?: [];
        
        $key = $method . '_' . $endpoint;
        if (!isset($stats[$key])) {
            $stats[$key] = ['count' => 0, 'total_time' => 0, 'errors' => 0];
        }
        
        $stats[$key]['count']++;
        $stats[$key]['total_time'] += $responseTime;
        if ($statusCode >= 400) {
            $stats[$key]['errors']++;
        }
        
        Cache::set($cacheKey, $stats, 3600); // 缓存1小时
        
        // 记录慢接口
        if ($responseTime > 2000) { // 超过2秒的接口
            Log::warning('慢接口检测', [
                'endpoint' => $endpoint,
                'method' => $method,
                'response_time' => $responseTime . 'ms',
                'status_code' => $statusCode,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 获取性能统计数据
     */
    public static function getPerformanceStats($date = null)
    {
        $date = $date ?: date('Y-m-d');
        $stats = [];
        
        // 获取24小时的统计数据
        for ($hour = 0; $hour < 24; $hour++) {
            $hourKey = $date . '-' . str_pad($hour, 2, '0', STR_PAD_LEFT);
            
            // 查询统计
            $queryStats = Cache::get(self::CACHE_PREFIX . 'query_stats_' . $hourKey) ?: [];
            
            // API统计
            $apiStats = Cache::get(self::CACHE_PREFIX . 'api_stats_' . $hourKey) ?: [];
            
            $stats[$hourKey] = [
                'query' => $queryStats,
                'api' => $apiStats
            ];
        }
        
        return $stats;
    }
    
    /**
     * 系统健康检查
     */
    public static function healthCheck()
    {
        $health = [
            'status' => 'healthy',
            'checks' => [],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 数据库连接检查
        try {
            Db::query('SELECT 1');
            $health['checks']['database'] = 'ok';
        } catch (\Exception $e) {
            $health['checks']['database'] = 'error: ' . $e->getMessage();
            $health['status'] = 'unhealthy';
        }
        
        // 缓存检查
        try {
            Cache::set('health_check', time(), 60);
            $value = Cache::get('health_check');
            $health['checks']['cache'] = $value ? 'ok' : 'error';
        } catch (\Exception $e) {
            $health['checks']['cache'] = 'error: ' . $e->getMessage();
            $health['status'] = 'unhealthy';
        }
        
        // 磁盘空间检查
        $freeSpace = disk_free_space('./');
        $totalSpace = disk_total_space('./');
        $usagePercent = (1 - $freeSpace / $totalSpace) * 100;
        
        if ($usagePercent > 90) {
            $health['checks']['disk'] = 'warning: ' . round($usagePercent, 2) . '% used';
            $health['status'] = 'warning';
        } else {
            $health['checks']['disk'] = 'ok: ' . round($usagePercent, 2) . '% used';
        }
        
        return $health;
    }
}
