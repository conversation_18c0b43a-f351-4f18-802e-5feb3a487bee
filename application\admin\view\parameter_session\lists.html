{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*参数填写会话管理用于监控用户的参数填写过程和会话状态。</p>
                        <p>*可以查看会话的完成度、剩余时间、参数值等详细信息。</p>
                        <p>*支持处理异常会话，如延长过期时间、标记完成等操作。</p>
                        <p>*提供会话数据导出功能，便于数据分析和统计。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-text-center">
                                    <div class="layui-font-32" id="total_sessions">0</div>
                                    <div class="layui-text-muted">总会话数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-text-center">
                                    <div class="layui-font-32 layui-text-green" id="completed_sessions">0</div>
                                    <div class="layui-text-muted">已完成</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-text-center">
                                    <div class="layui-font-32 layui-text-blue" id="active_sessions">0</div>
                                    <div class="layui-text-muted">进行中</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-text-center">
                                    <div class="layui-font-32 layui-text-orange" id="expired_sessions">0</div>
                                    <div class="layui-text-muted">已过期</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-card">
            <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">会话状态:</label>
                        <div class="layui-input-block">
                            <select name="status" id="status">
                                <option value="">全部状态</option>
                                <option value="active">进行中</option>
                                <option value="completed">已完成</option>
                                <option value="expired">已过期</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">完成度:</label>
                        <div class="layui-input-block">
                            <select name="completion_rate" id="completion_rate">
                                <option value="">全部完成度</option>
                                <option value="0-25">0-25%</option>
                                <option value="26-50">26-50%</option>
                                <option value="51-75">51-75%</option>
                                <option value="76-100">76-100%</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">时间范围:</label>
                        <div class="layui-input-block">
                            <input type="text" name="time_range" id="time_range" placeholder="请选择时间范围" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">关键词:</label>
                        <div class="layui-input-block">
                            <input type="text" name="search_keyword" id="search_keyword" placeholder="用户昵称/手机号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-card-body">
                <div style="padding-bottom: 10px;">
                    <button class="layui-btn layui-btn-sm" id="batch_extend">
                        <i class="layui-icon layui-icon-time"></i>批量延长
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" id="process_abnormal">
                        <i class="layui-icon layui-icon-tips"></i>处理异常
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" id="export_data">
                        <i class="layui-icon layui-icon-export"></i>导出数据
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" id="refresh_list">
                        <i class="layui-icon layui-icon-refresh"></i>刷新
                    </button>
                </div>
                
                <table class="layui-hide" id="session_table" lay-filter="session_table"></table>
                
                <script type="text/html" id="user_info_tpl">
                    <div style="max-width: 150px;">
                        <div><strong>{{d.nickname}}</strong></div>
                        <div class="layui-text-muted" style="font-size: 12px;">{{d.mobile}}</div>
                    </div>
                </script>
                
                <script type="text/html" id="goods_info_tpl">
                    <div style="max-width: 180px;">
                        <div><strong>{{d.goods_name}}</strong></div>
                        <div class="layui-text-muted" style="font-size: 12px;">ID: {{d.goods_id}}</div>
                    </div>
                </script>
                
                <script type="text/html" id="completion_rate_tpl">
                    <div class="layui-progress" lay-showpercent="true">
                        <div class="layui-progress-bar" lay-percent="{{d.completion_rate}}%"></div>
                    </div>
                </script>
                
                <script type="text/html" id="status_tpl">
                    {{# if(d.status == 'active'){ }}
                        <span class="layui-badge layui-bg-blue">进行中</span>
                    {{# } else if(d.status == 'completed'){ }}
                        <span class="layui-badge layui-bg-green">已完成</span>
                    {{# } else if(d.status == 'expired'){ }}
                        <span class="layui-badge layui-bg-red">已过期</span>
                    {{# } else { }}
                        <span class="layui-badge layui-bg-gray">{{d.status_text}}</span>
                    {{# } }}
                </script>
                
                <script type="text/html" id="remaining_time_tpl">
                    {{# if(d.remaining_hours > 0){ }}
                        <span class="layui-text-green">{{d.remaining_hours}}小时</span>
                    {{# } else { }}
                        <span class="layui-text-red">已过期</span>
                    {{# } }}
                </script>
                
                <script type="text/html" id="action_tpl">
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">详情</a>
                    {{# if(d.status == 'active'){ }}
                        <a class="layui-btn layui-btn-xs" lay-event="extend">延长</a>
                    {{# } else if(d.status == 'expired'){ }}
                        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="reactivate">重新激活</a>
                    {{# } }}
                    {{# if(d.status != 'completed'){ }}
                        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
                    {{# } }}
                </script>
            </div>
        </div>
    </div>
</div>

<!-- 会话详情弹窗 -->
<div id="session_detail" style="display: none; padding: 20px;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">会话信息</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <tbody>
                            <tr><td>会话ID</td><td id="detail_session_id">-</td></tr>
                            <tr><td>完成度</td><td id="detail_completion_rate">-</td></tr>
                            <tr><td>状态</td><td id="detail_status">-</td></tr>
                            <tr><td>剩余时间</td><td id="detail_remaining_time">-</td></tr>
                            <tr><td>创建时间</td><td id="detail_created_at">-</td></tr>
                            <tr><td>过期时间</td><td id="detail_expires_at">-</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">用户信息</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <tbody>
                            <tr><td>用户昵称</td><td id="detail_nickname">-</td></tr>
                            <tr><td>手机号</td><td id="detail_mobile">-</td></tr>
                            <tr><td>用户ID</td><td id="detail_user_id">-</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">商品信息</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <tbody>
                            <tr><td>商品名称</td><td id="detail_goods_name">-</td></tr>
                            <tr><td>商品ID</td><td id="detail_goods_id">-</td></tr>
                            <tr><td>商品价格</td><td id="detail_goods_price">-</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">模板信息</div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <tbody>
                            <tr><td>模板名称</td><td id="detail_template_name">-</td></tr>
                            <tr><td>模板ID</td><td id="detail_template_id">-</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="layui-card" style="margin-top: 15px;">
        <div class="layui-card-header">参数值</div>
        <div class="layui-card-body">
            <div id="detail_parameter_values">
                <!-- 动态填充参数值 -->
            </div>
        </div>
    </div>
</div>

<!-- 延长过期时间表单 -->
<div id="extend_form" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="extend_form">
        <input type="hidden" name="session_id" id="extend_session_id">
        
        <div class="layui-form-item">
            <label class="layui-form-label">延长时间</label>
            <div class="layui-input-block">
                <select name="extend_hours" lay-verify="required">
                    <option value="">请选择延长时间</option>
                    <option value="6">6小时</option>
                    <option value="12">12小时</option>
                    <option value="24">24小时</option>
                    <option value="48">48小时</option>
                    <option value="72">72小时</option>
                </select>
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入延长原因" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submit_extend">确认延长</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>

<script>
layui.use(['table', 'form', 'laydate', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var laydate = layui.laydate;
    var layer = layui.layer;
    
    // 时间范围选择器
    laydate.render({
        elem: '#time_range',
        type: 'date',
        range: true
    });
    
    // 数据表格
    var tableIns = table.render({
        elem: '#session_table',
        url: '{:url("parameter_session/lists")}',
        method: 'GET',
        page: true,
        cols: [[
            {type: 'checkbox', width: 60, fixed: 'left'},
            {field: 'id', title: '会话ID', width: 180, fixed: 'left'},
            {field: 'user_info', title: '用户信息', width: 160, templet: '#user_info_tpl'},
            {field: 'goods_info', title: '商品信息', width: 200, templet: '#goods_info_tpl'},
            {field: 'completion_rate', title: '完成度', width: 120, templet: '#completion_rate_tpl'},
            {field: 'status', title: '状态', width: 100, templet: '#status_tpl'},
            {field: 'remaining_time', title: '剩余时间', width: 100, templet: '#remaining_time_tpl'},
            {field: 'created_at', title: '创建时间', width: 160},
            {field: 'expires_at', title: '过期时间', width: 160},
            {title: '操作', width: 200, toolbar: '#action_tpl', fixed: 'right'}
        ]],
        limit: 20,
        limits: [10, 20, 50, 100],
        done: function(res) {
            // 更新统计数据
            updateStatistics(res.statistics || {});
        }
    });
    
    // 搜索
    form.on('submit(search)', function(data){
        var field = data.field;
        tableIns.reload({
            where: field,
            page: {curr: 1}
        });
        return false;
    });
    
    // 工具栏事件
    table.on('tool(session_table)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;
        
        if(layEvent === 'detail'){
            showSessionDetail(data.id);
        } else if(layEvent === 'extend'){
            extendSession(data.id);
        } else if(layEvent === 'reactivate'){
            reactivateSession(data.id);
        } else if(layEvent === 'delete'){
            deleteSession(data.id);
        }
    });
    
    // 批量延长
    $('#batch_extend').on('click', function(){
        var checkStatus = table.checkStatus('session_table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要延长的会话');
            return;
        }
        
        batchExtendSessions(data);
    });
    
    // 处理异常
    $('#process_abnormal').on('click', function(){
        processAbnormalSessions();
    });
    
    // 导出数据
    $('#export_data').on('click', function(){
        exportSessionData();
    });
    
    // 刷新列表
    $('#refresh_list').on('click', function(){
        tableIns.reload();
    });
    
    // 更新统计数据
    function updateStatistics(stats) {
        $('#total_sessions').text(stats.total_sessions || 0);
        $('#completed_sessions').text(stats.completed_sessions || 0);
        $('#active_sessions').text(stats.active_sessions || 0);
        $('#expired_sessions').text(stats.expired_sessions || 0);
    }
    
    // 显示会话详情
    function showSessionDetail(sessionId) {
        var loadIndex = layer.load(2);
        $.get('{:url("parameter_session/detail")}', {session_id: sessionId}, function(res){
            layer.close(loadIndex);
            if(res.code == 1){
                var data = res.data;
                
                // 填充详情数据
                $('#detail_session_id').text(data.session_info.id);
                $('#detail_completion_rate').html('<div class="layui-progress" lay-showpercent="true"><div class="layui-progress-bar" lay-percent="' + data.session_info.completion_rate + '%"></div></div>');
                $('#detail_status').text(data.session_info.status_text);
                $('#detail_remaining_time').text(data.session_info.remaining_hours + '小时');
                $('#detail_created_at').text(data.session_info.created_at);
                $('#detail_expires_at').text(data.session_info.expires_at);
                
                $('#detail_nickname').text(data.user_info.nickname);
                $('#detail_mobile').text(data.user_info.mobile);
                $('#detail_user_id').text(data.user_info.user_id);
                
                $('#detail_goods_name').text(data.goods_info.name);
                $('#detail_goods_id').text(data.goods_info.id);
                $('#detail_goods_price').text('¥' + data.goods_info.price);
                
                $('#detail_template_name').text(data.template_config.name);
                $('#detail_template_id').text(data.template_config.id);
                
                // 填充参数值
                var parameterHtml = '';
                if(data.parameter_values && Object.keys(data.parameter_values).length > 0){
                    for(var key in data.parameter_values){
                        parameterHtml += '<div class="layui-form-item">';
                        parameterHtml += '<label class="layui-form-label">' + key + ':</label>';
                        parameterHtml += '<div class="layui-input-block">';
                        parameterHtml += '<input type="text" value="' + data.parameter_values[key] + '" readonly class="layui-input">';
                        parameterHtml += '</div></div>';
                    }
                } else {
                    parameterHtml = '<div class="layui-text-muted">暂无参数值</div>';
                }
                $('#detail_parameter_values').html(parameterHtml);
                
                layer.open({
                    type: 1,
                    title: '会话详情',
                    area: ['900px', '600px'],
                    content: $('#session_detail')
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
    }
    
    // 延长会话
    function extendSession(sessionId) {
        $('#extend_session_id').val(sessionId);
        
        layer.open({
            type: 1,
            title: '延长过期时间',
            area: ['500px', '400px'],
            content: $('#extend_form'),
            success: function(layero, index){
                form.render();
            }
        });
    }
    
    // 提交延长
    form.on('submit(submit_extend)', function(data){
        var field = data.field;
        
        var loadIndex = layer.load(2);
        $.post('{:url("parameter_session/extend")}', field, function(res){
            layer.close(loadIndex);
            if(res.code == 1){
                layer.msg(res.msg, {icon: 1});
                layer.closeAll('page');
                tableIns.reload();
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        
        return false;
    });
    
    // 重新激活会话
    function reactivateSession(sessionId) {
        layer.confirm('确定要重新激活此会话吗？', function(index){
            layer.close(index);
            
            var loadIndex = layer.load(2);
            $.post('{:url("parameter_session/reactivate")}', {session_id: sessionId}, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        });
    }
    
    // 删除会话
    function deleteSession(sessionId) {
        layer.confirm('确定要删除此会话吗？删除后不可恢复。', function(index){
            layer.close(index);
            
            var loadIndex = layer.load(2);
            $.post('{:url("parameter_session/delete")}', {session_id: sessionId}, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        });
    }
    
    // 批量延长会话
    function batchExtendSessions(sessions) {
        layer.msg('批量延长功能开发中...', {icon: 6});
    }
    
    // 处理异常会话
    function processAbnormalSessions() {
        layer.confirm('确定要处理异常会话吗？系统将自动处理过期和异常状态的会话。', function(index){
            layer.close(index);
            
            var loadIndex = layer.load(2);
            $.post('{:url("parameter_session/process_abnormal")}', {}, function(res){
                layer.close(loadIndex);
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        });
    }
    
    // 导出会话数据
    function exportSessionData() {
        layer.msg('数据导出功能开发中...', {icon: 6});
    }
});
</script>
