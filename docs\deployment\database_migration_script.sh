#!/bin/bash
# 模板商品化系统数据库迁移脚本
# 版本: v1.0.0
# 创建时间: 2024-01-15
# 说明: 用于生产环境的数据库迁移和升级

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-yinshua.zueseo.cn}"
DB_USER="${DB_USER:-root}"
DB_PASS="${DB_PASS:-root}"
BACKUP_DIR="${BACKUP_DIR:-/backup/likeshop/database}"
MIGRATION_DIR="${MIGRATION_DIR:-./database/migrations}"
LOG_FILE="${LOG_FILE:-/var/log/likeshop/migration.log}"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✓${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ✗${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    # 检查MySQL客户端
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装"
        exit 1
    fi
    
    # 检查mysqldump
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump未安装"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 检查数据库连接
check_database_connection() {
    log "检查数据库连接..."
    
    if [ -z "$DB_PASS" ]; then
        read -s -p "请输入数据库密码: " DB_PASS
        echo
    fi
    
    # 测试连接
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &> /dev/null; then
        log_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 创建备份
create_backup() {
    log "创建数据库备份..."
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 生成备份文件名
    BACKUP_FILE="$BACKUP_DIR/migration_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # 执行备份
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --add-drop-table \
        --comments \
        "$DB_NAME" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        # 压缩备份文件
        gzip "$BACKUP_FILE"
        log_success "数据库备份完成: ${BACKUP_FILE}.gz"
        echo "$BACKUP_FILE.gz" > /tmp/migration_backup_path
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 检查表结构
check_table_structure() {
    log "检查当前表结构..."
    
    # 检查核心表是否存在
    TABLES_TO_CHECK=(
        "ls_goods"
        "ls_order"
        "ls_user"
        "ls_admin"
    )
    
    for table in "${TABLES_TO_CHECK[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
           -e "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            log_success "表 $table 存在"
        else
            log_error "表 $table 不存在，请检查数据库"
            exit 1
        fi
    done
}

# 执行迁移脚本
execute_migration() {
    local migration_file="$1"
    local migration_name=$(basename "$migration_file" .sql)
    
    log "执行迁移: $migration_name"
    
    # 检查迁移是否已执行
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
       -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME' AND table_name='ls_migrations';" \
       | tail -1 | grep -q "1"; then
        
        # 检查迁移记录
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
           -e "SELECT COUNT(*) FROM ls_migrations WHERE migration='$migration_name';" \
           | tail -1 | grep -q "1"; then
            log_warning "迁移 $migration_name 已执行，跳过"
            return 0
        fi
    else
        # 创建迁移记录表
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" << EOF
CREATE TABLE IF NOT EXISTS ls_migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    migration VARCHAR(255) NOT NULL UNIQUE,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_migration (migration)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库迁移记录表';
EOF
    fi
    
    # 执行迁移文件
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$migration_file"; then
        # 记录迁移
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
            -e "INSERT INTO ls_migrations (migration) VALUES ('$migration_name');"
        log_success "迁移 $migration_name 执行成功"
    else
        log_error "迁移 $migration_name 执行失败"
        return 1
    fi
}

# 执行所有迁移
run_migrations() {
    log "开始执行数据库迁移..."
    
    # 检查迁移目录
    if [ ! -d "$MIGRATION_DIR" ]; then
        log_error "迁移目录不存在: $MIGRATION_DIR"
        exit 1
    fi
    
    # 按文件名排序执行迁移
    local migration_files=($(find "$MIGRATION_DIR" -name "*.sql" | sort))
    
    if [ ${#migration_files[@]} -eq 0 ]; then
        log_warning "没有找到迁移文件"
        return 0
    fi
    
    local success_count=0
    local total_count=${#migration_files[@]}
    
    for migration_file in "${migration_files[@]}"; do
        if execute_migration "$migration_file"; then
            ((success_count++))
        else
            log_error "迁移失败，停止执行"
            break
        fi
    done
    
    log "迁移执行完成: $success_count/$total_count"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有迁移执行成功"
        return 0
    else
        log_error "部分迁移执行失败"
        return 1
    fi
}

# 验证迁移结果
verify_migration() {
    log "验证迁移结果..."
    
    # 检查新表是否创建成功
    NEW_TABLES=(
        "ls_group_buy_activities"
        "ls_parameter_fill_sessions"
        "ls_service_delivery_records"
        "ls_template_order_params"
    )
    
    for table in "${NEW_TABLES[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
           -e "SHOW TABLES LIKE '$table';" | grep -q "$table"; then
            log_success "新表 $table 创建成功"
        else
            log_error "新表 $table 创建失败"
            return 1
        fi
    done
    
    # 检查商品表扩展字段
    GOODS_FIELDS=(
        "goods_type"
        "service_config"
    )
    
    for field in "${GOODS_FIELDS[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
           -e "DESCRIBE ls_goods $field;" &> /dev/null; then
            log_success "商品表字段 $field 扩展成功"
        else
            log_error "商品表字段 $field 扩展失败"
            return 1
        fi
    done
    
    # 检查订单表扩展字段
    ORDER_FIELDS=(
        "order_type"
    )
    
    for field in "${ORDER_FIELDS[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" \
           -e "DESCRIBE ls_order $field;" &> /dev/null; then
            log_success "订单表字段 $field 扩展成功"
        else
            log_error "订单表字段 $field 扩展失败"
            return 1
        fi
    done
    
    log_success "迁移结果验证通过"
}

# 回滚函数
rollback_migration() {
    log_warning "开始回滚数据库..."
    
    if [ -f "/tmp/migration_backup_path" ]; then
        BACKUP_FILE=$(cat /tmp/migration_backup_path)
        
        if [ -f "$BACKUP_FILE" ]; then
            log "从备份文件恢复: $BACKUP_FILE"
            
            # 解压并恢复
            if [[ "$BACKUP_FILE" == *.gz ]]; then
                zcat "$BACKUP_FILE" | mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME"
            else
                mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$BACKUP_FILE"
            fi
            
            if [ $? -eq 0 ]; then
                log_success "数据库回滚成功"
            else
                log_error "数据库回滚失败"
            fi
        else
            log_error "备份文件不存在: $BACKUP_FILE"
        fi
    else
        log_error "未找到备份文件路径"
    fi
}

# 清理函数
cleanup() {
    log "清理临时文件..."
    rm -f /tmp/migration_backup_path
}

# 信号处理
trap 'log_error "迁移被中断"; cleanup; exit 1' INT TERM

# 显示帮助信息
show_help() {
    cat << EOF
模板商品化系统数据库迁移脚本

使用方法:
    $0 [选项]

选项:
    -h, --help          显示帮助信息
    -b, --backup-only   仅创建备份
    -r, --rollback      回滚到备份
    -v, --verify-only   仅验证迁移结果
    --dry-run          预演模式（不执行实际迁移）

环境变量:
    DB_HOST            数据库主机 (默认: localhost)
    DB_PORT            数据库端口 (默认: 3306)
    DB_NAME            数据库名称 (默认: likeshop_prod)
    DB_USER            数据库用户 (默认: likeshop)
    DB_PASS            数据库密码
    BACKUP_DIR         备份目录 (默认: /backup/likeshop/database)
    MIGRATION_DIR      迁移文件目录 (默认: ./database/migrations)

示例:
    # 完整迁移
    $0
    
    # 仅创建备份
    $0 --backup-only
    
    # 回滚数据库
    $0 --rollback
    
    # 使用自定义配置
    DB_HOST=************* DB_USER=root $0

EOF
}

# 主函数
main() {
    local backup_only=false
    local rollback=false
    local verify_only=false
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--backup-only)
                backup_only=true
                shift
                ;;
            -r|--rollback)
                rollback=true
                shift
                ;;
            -v|--verify-only)
                verify_only=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "开始模板商品化系统数据库迁移"
    log "配置信息:"
    log "  数据库主机: $DB_HOST:$DB_PORT"
    log "  数据库名称: $DB_NAME"
    log "  数据库用户: $DB_USER"
    log "  备份目录: $BACKUP_DIR"
    log "  迁移目录: $MIGRATION_DIR"
    
    # 检查依赖
    check_dependencies
    
    # 检查数据库连接
    check_database_connection
    
    if [ "$rollback" = true ]; then
        rollback_migration
        cleanup
        exit 0
    fi
    
    if [ "$verify_only" = true ]; then
        verify_migration
        exit $?
    fi
    
    # 检查表结构
    check_table_structure
    
    # 创建备份
    create_backup
    
    if [ "$backup_only" = true ]; then
        log_success "备份创建完成"
        cleanup
        exit 0
    fi
    
    if [ "$dry_run" = true ]; then
        log_warning "预演模式：不执行实际迁移"
        cleanup
        exit 0
    fi
    
    # 执行迁移
    if run_migrations; then
        # 验证迁移结果
        if verify_migration; then
            log_success "数据库迁移完成！"
        else
            log_error "迁移验证失败，建议检查数据库状态"
            exit 1
        fi
    else
        log_error "数据库迁移失败"
        read -p "是否要回滚到备份？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rollback_migration
        fi
        exit 1
    fi
    
    cleanup
    log_success "迁移脚本执行完成"
}

# 执行主函数
main "$@"
