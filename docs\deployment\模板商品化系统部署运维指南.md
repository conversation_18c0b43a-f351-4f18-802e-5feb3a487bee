# 模板商品化系统部署运维指南

## 概述

本文档为运维团队提供模板商品化系统的完整部署和运维指南，确保系统能够顺利部署到生产环境并稳定运行。

## 系统架构

### 技术栈
- **后端框架**: ThinkPHP 5.1
- **数据库**: MySQL 5.7+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+
- **PHP版本**: PHP 7.4+
- **外部服务**: 迅排设计API服务

### 系统组件
- **主应用**: LikeShop商城系统（扩展版）
- **海报生成服务**: 迅排设计API服务
- **数据库**: MySQL主从架构
- **缓存层**: Redis集群
- **文件存储**: 本地存储/OSS
- **负载均衡**: Nginx

## 环境要求

### 硬件要求

#### 生产环境（推荐配置）
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **存储**: SSD 500GB以上
- **网络**: 千兆网络

#### 测试环境（最低配置）
- **CPU**: 4核心
- **内存**: 8GB
- **存储**: SSD 200GB
- **网络**: 百兆网络

### 软件要求

#### 操作系统
- CentOS 7.6+ / Ubuntu 18.04+
- 内核版本 3.10+

#### 基础软件
```bash
# PHP 7.4+
php >= 7.4.0
php-fpm
php-mysql
php-redis
php-gd
php-curl
php-json
php-mbstring
php-xml
php-zip

# MySQL 5.7+
mysql-server >= 5.7.0

# Redis 6.0+
redis-server >= 6.0.0

# Nginx 1.18+
nginx >= 1.18.0

# 其他工具
git
composer
supervisor
```

## 部署前准备

### 1. 服务器准备

#### 创建部署用户
```bash
# 创建部署用户
useradd -m -s /bin/bash deploy
usermod -aG sudo deploy

# 设置SSH密钥认证
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
# 将公钥添加到 /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R deploy:deploy /home/<USER>/.ssh
```

#### 创建目录结构
```bash
# 应用目录
mkdir -p /var/www/likeshop
mkdir -p /var/www/likeshop/releases
mkdir -p /var/www/likeshop/shared
mkdir -p /var/www/likeshop/shared/runtime
mkdir -p /var/www/likeshop/shared/uploads

# 日志目录
mkdir -p /var/log/likeshop
mkdir -p /var/log/nginx/likeshop

# 备份目录
mkdir -p /backup/likeshop/database
mkdir -p /backup/likeshop/files

# 设置权限
chown -R deploy:deploy /var/www/likeshop
chown -R deploy:deploy /var/log/likeshop
```

### 2. 数据库准备

#### MySQL安装和配置
```bash
# 安装MySQL
yum install -y mysql-server mysql

# 启动MySQL服务
systemctl start mysqld
systemctl enable mysqld

# 安全配置
mysql_secure_installation
```

#### 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE likeshop_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'likeshop'@'localhost' IDENTIFIED BY 'your_secure_password';
CREATE USER 'likeshop'@'%' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON likeshop_prod.* TO 'likeshop'@'localhost';
GRANT ALL PRIVILEGES ON likeshop_prod.* TO 'likeshop'@'%';
FLUSH PRIVILEGES;
```

#### MySQL优化配置
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0
max_connections = 1000
max_connect_errors = 10000

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# InnoDB配置
innodb_buffer_pool_size = 8G
innodb_log_file_size = 512M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
```

### 3. Redis安装和配置

#### Redis安装
```bash
# 安装Redis
yum install -y redis

# 启动Redis服务
systemctl start redis
systemctl enable redis
```

#### Redis配置优化
```ini
# /etc/redis.conf
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 60

# 内存配置
maxmemory 4gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF配置
appendonly yes
appendfsync everysec

# 安全配置
requirepass your_redis_password
```

### 4. Nginx安装和配置

#### Nginx安装
```bash
# 安装Nginx
yum install -y nginx

# 启动Nginx服务
systemctl start nginx
systemctl enable nginx
```

## 数据库迁移

### 1. 备份现有数据库
```bash
#!/bin/bash
# backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/likeshop/database"
DB_NAME="likeshop_prod"
DB_USER="likeshop"
DB_PASS="your_secure_password"

# 创建备份
mysqldump -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  $DB_NAME > $BACKUP_DIR/likeshop_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/likeshop_backup_$DATE.sql

echo "数据库备份完成: likeshop_backup_$DATE.sql.gz"
```

### 2. 执行数据库迁移
```bash
#!/bin/bash
# migrate_database.sh

DB_NAME="likeshop_prod"
DB_USER="likeshop"
DB_PASS="your_secure_password"
MIGRATION_DIR="/var/www/likeshop/current/database/migrations"

echo "开始执行数据库迁移..."

# 执行完整迁移脚本
mysql -u$DB_USER -p$DB_PASS $DB_NAME < $MIGRATION_DIR/COMPLETE_DATABASE_MIGRATION.sql

if [ $? -eq 0 ]; then
    echo "数据库迁移成功完成"
else
    echo "数据库迁移失败，请检查错误信息"
    exit 1
fi

# 执行性能优化索引
mysql -u$DB_USER -p$DB_PASS $DB_NAME < $MIGRATION_DIR/../sql/performance_optimization_indexes.sql

if [ $? -eq 0 ]; then
    echo "性能优化索引创建完成"
else
    echo "性能优化索引创建失败"
fi

echo "数据库迁移和优化完成"
```

### 3. 验证数据库迁移
```sql
-- 验证新表是否创建成功
SHOW TABLES LIKE 'ls_group_buy_activities';
SHOW TABLES LIKE 'ls_parameter_fill_sessions';
SHOW TABLES LIKE 'ls_service_delivery_records';
SHOW TABLES LIKE 'ls_template_order_params';

-- 验证商品表扩展字段
DESCRIBE ls_goods;

-- 验证订单表扩展字段
DESCRIBE ls_order;

-- 检查索引创建情况
SHOW INDEX FROM ls_goods WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM ls_order WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM ls_group_buy_activities WHERE Key_name LIKE 'idx_%';
```

## 应用部署

### 1. 代码部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

# 配置变量
APP_NAME="likeshop"
DEPLOY_USER="deploy"
DEPLOY_PATH="/var/www/likeshop"
REPO_URL="https://github.com/your-org/likeshop-server.git"
BRANCH="main"
RELEASE_DIR="$DEPLOY_PATH/releases/$(date +%Y%m%d_%H%M%S)"
SHARED_DIR="$DEPLOY_PATH/shared"
CURRENT_DIR="$DEPLOY_PATH/current"

echo "开始部署 $APP_NAME..."

# 创建发布目录
mkdir -p $RELEASE_DIR

# 克隆代码
echo "正在克隆代码..."
git clone -b $BRANCH $REPO_URL $RELEASE_DIR

# 进入发布目录
cd $RELEASE_DIR

# 安装依赖
echo "正在安装依赖..."
composer install --no-dev --optimize-autoloader --no-interaction

# 创建符号链接到共享目录
echo "正在创建符号链接..."
rm -rf $RELEASE_DIR/runtime
ln -s $SHARED_DIR/runtime $RELEASE_DIR/runtime

rm -rf $RELEASE_DIR/public/uploads
ln -s $SHARED_DIR/uploads $RELEASE_DIR/public/uploads

# 复制配置文件
echo "正在复制配置文件..."
cp $SHARED_DIR/.env $RELEASE_DIR/.env

# 设置权限
echo "正在设置权限..."
chown -R $DEPLOY_USER:$DEPLOY_USER $RELEASE_DIR
chmod -R 755 $RELEASE_DIR
chmod -R 777 $RELEASE_DIR/runtime

# 更新当前版本链接
echo "正在更新当前版本..."
rm -f $CURRENT_DIR
ln -s $RELEASE_DIR $CURRENT_DIR

# 重启PHP-FPM
echo "正在重启PHP-FPM..."
systemctl reload php-fpm

# 清理旧版本（保留最近5个版本）
echo "正在清理旧版本..."
cd $DEPLOY_PATH/releases
ls -t | tail -n +6 | xargs -r rm -rf

echo "部署完成！"
echo "当前版本: $(basename $RELEASE_DIR)"
```

### 2. 环境配置文件
```bash
# /var/www/likeshop/shared/.env

[app]
app_debug = false
app_trace = false

[database]
hostname = 127.0.0.1
database = likeshop_prod
username = likeshop
password = your_secure_password
hostport = 3306
prefix = ls_

[cache]
type = Redis
host = 127.0.0.1
port = 6379
password = your_redis_password

[project]
file_domain = https://your-domain.com
sms = true
version = 3.0.3.20231204

# 海报模板服务配置
[poster]
api_base_url = http://localhost:7001
api_timeout = 30
external_api_key = your-secure-api-key
cache_enabled = true
template_cache_ttl = 600
max_width = 4000
max_height = 4000
default_quality = 0.9
mock_enabled = false
debug_mode = false
test_mode = false
storage_disk = local
storage_path = poster_template
storage_url_prefix = /uploads/poster_template
```

### 3. Nginx配置
```nginx
# /etc/nginx/sites-available/likeshop
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    root /var/www/likeshop/current/public;
    index index.php index.html;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/likeshop/access.log;
    error_log /var/log/nginx/likeshop/error.log;
    
    # 客户端上传限制
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 上传文件目录
    location /uploads/ {
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(sql|log|md)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # PHP处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 超时设置
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        fastcgi_buffer_size 64k;
        fastcgi_buffers 4 64k;
        fastcgi_busy_buffers_size 128k;
    }
    
    # URL重写
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # API接口限流
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
}

# 限流配置
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}
```

### 4. PHP-FPM配置
```ini
# /etc/php/7.4/fpm/pool.d/likeshop.conf
[likeshop]
user = deploy
group = deploy
listen = /var/run/php/php7.4-fpm-likeshop.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 1000

# 超时设置
request_terminate_timeout = 60s
request_slowlog_timeout = 30s
slowlog = /var/log/php/likeshop-slow.log

# 环境变量
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

# PHP配置
php_admin_value[error_log] = /var/log/php/likeshop-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 60
php_admin_value[upload_max_filesize] = 50M
php_admin_value[post_max_size] = 50M
```

## 外部服务配置

### 1. 迅排设计API服务部署

#### Docker部署方式
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主应用服务
  likeshop:
    build: .
    ports:
      - "80:80"
      - "443:443"
    environment:
      - APP_ENV=production
      - DATABASE_URL=mysql://likeshop:password@mysql:3306/likeshop_prod
      - REDIS_URL=redis://redis:6379
      - POSTER_API_URL=http://poster-service:7001
      - POSTER_API_KEY=your-secure-api-key
    depends_on:
      - mysql
      - redis
      - poster-service
    volumes:
      - ./uploads:/var/www/likeshop/current/public/uploads
      - ./logs:/var/log/likeshop
    networks:
      - likeshop-network

  # 迅排设计服务
  poster-service:
    image: heimanba/poster-api:latest
    ports:
      - "7001:7001"
    environment:
      - NODE_ENV=production
      - EXTERNAL_API_URL=http://likeshop:80/api
      - EXTERNAL_API_KEY=your-secure-api-key
      - PARAMETER_CACHE_ENABLED=true
      - CACHE_TTL=600
    volumes:
      - poster-cache:/cache
      - poster-templates:/templates
    networks:
      - likeshop-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=likeshop_prod
      - MYSQL_USER=likeshop
      - MYSQL_PASSWORD=password
    volumes:
      - mysql-data:/var/lib/mysql
      - ./database/migrations:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - likeshop-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    command: redis-server --requirepass your_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - likeshop-network
    restart: unless-stopped

volumes:
  mysql-data:
  redis-data:
  poster-cache:
  poster-templates:

networks:
  likeshop-network:
    driver: bridge
```

#### 传统部署方式
```bash
#!/bin/bash
# deploy_poster_service.sh

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
apt-get install -y nodejs

# 创建服务目录
mkdir -p /opt/poster-service
cd /opt/poster-service

# 下载并解压服务
wget https://github.com/heimanba/poster-api/releases/latest/download/poster-api.tar.gz
tar -xzf poster-api.tar.gz

# 安装依赖
npm install --production

# 创建配置文件
cat > config/production.json << EOF
{
  "port": 7001,
  "external_api": {
    "base_url": "https://your-domain.com/api",
    "api_key": "your-secure-api-key",
    "timeout": 30000
  },
  "cache": {
    "enabled": true,
    "ttl": 600,
    "max_size": 1000
  },
  "image": {
    "max_width": 4000,
    "max_height": 4000,
    "default_quality": 0.9,
    "formats": ["jpg", "png", "webp"]
  },
  "logging": {
    "level": "info",
    "file": "/var/log/poster-service/app.log"
  }
}
EOF

# 创建systemd服务
cat > /etc/systemd/system/poster-service.service << EOF
[Unit]
Description=Poster Template Service
After=network.target

[Service]
Type=simple
User=deploy
WorkingDirectory=/opt/poster-service
ExecStart=/usr/bin/node app.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
systemctl daemon-reload
systemctl enable poster-service
systemctl start poster-service
```

### 2. 服务健康检查
```bash
#!/bin/bash
# health_check.sh

# 检查主应用
echo "检查主应用状态..."
curl -f http://localhost/api/health || echo "主应用异常"

# 检查迅排设计服务
echo "检查迅排设计服务状态..."
curl -f http://localhost:7001/health || echo "迅排设计服务异常"

# 检查数据库连接
echo "检查数据库连接..."
mysql -u likeshop -p'password' -e "SELECT 1" likeshop_prod || echo "数据库连接异常"

# 检查Redis连接
echo "检查Redis连接..."
redis-cli -a 'your_redis_password' ping || echo "Redis连接异常"

# 检查磁盘空间
echo "检查磁盘空间..."
df -h | grep -E "(/$|/var)" | awk '{if($5+0 > 80) print "磁盘空间不足: " $0}'

# 检查内存使用
echo "检查内存使用..."
free -m | awk 'NR==2{printf "内存使用率: %.2f%%\n", $3*100/$2}'

# 检查CPU负载
echo "检查CPU负载..."
uptime | awk '{print "CPU负载: " $(NF-2) " " $(NF-1) " " $NF}'
```

## 监控和日志

### 1. 系统监控配置

#### Supervisor进程管理
```ini
# /etc/supervisor/conf.d/likeshop.conf
[program:likeshop-queue]
command=php /var/www/likeshop/current/think queue:work
directory=/var/www/likeshop/current
user=deploy
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/likeshop/queue.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=5

[program:likeshop-cron]
command=php /var/www/likeshop/current/think cron:run
directory=/var/www/likeshop/current
user=deploy
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/likeshop/cron.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
```

#### 日志轮转配置
```bash
# /etc/logrotate.d/likeshop
/var/log/likeshop/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deploy deploy
    postrotate
        systemctl reload php7.4-fpm
    endscript
}

/var/log/nginx/likeshop/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 2. 性能监控脚本
```bash
#!/bin/bash
# performance_monitor.sh

LOG_FILE="/var/log/likeshop/performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 获取系统负载
LOAD=$(uptime | awk '{print $(NF-2)}' | sed 's/,//')

# 获取内存使用率
MEMORY=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')

# 获取磁盘使用率
DISK=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')

# 获取MySQL连接数
MYSQL_CONN=$(mysql -u likeshop -p'password' -e "SHOW STATUS LIKE 'Threads_connected';" | tail -1 | awk '{print $2}')

# 获取Redis内存使用
REDIS_MEM=$(redis-cli -a 'your_redis_password' info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')

# 记录到日志
echo "$DATE,LOAD:$LOAD,MEMORY:${MEMORY}%,DISK:${DISK}%,MYSQL_CONN:$MYSQL_CONN,REDIS_MEM:$REDIS_MEM" >> $LOG_FILE

# 检查阈值并发送告警
if (( $(echo "$LOAD > 5.0" | bc -l) )); then
    echo "告警: 系统负载过高 $LOAD" | mail -s "LikeShop系统告警" <EMAIL>
fi

if (( $(echo "$MEMORY > 90" | bc -l) )); then
    echo "告警: 内存使用率过高 ${MEMORY}%" | mail -s "LikeShop系统告警" <EMAIL>
fi

if [ "$DISK" -gt 85 ]; then
    echo "告警: 磁盘使用率过高 ${DISK}%" | mail -s "LikeShop系统告警" <EMAIL>
fi
```

### 3. 应用监控配置
```php
<?php
// application/common/behavior/PerformanceMonitor.php

namespace app\common\behavior;

use think\Log;
use think\Cache;

class PerformanceMonitor
{
    public function run($params)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // 注册结束回调
        register_shutdown_function(function() use ($startTime, $startMemory) {
            $endTime = microtime(true);
            $endMemory = memory_get_usage();

            $executionTime = round(($endTime - $startTime) * 1000, 2);
            $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2);

            // 记录性能数据
            $performanceData = [
                'url' => request()->url(),
                'method' => request()->method(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage,
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // 慢请求告警
            if ($executionTime > 2000) {
                Log::warning('慢请求检测', $performanceData);
            }

            // 高内存使用告警
            if ($memoryUsage > 50) {
                Log::warning('高内存使用检测', $performanceData);
            }

            // 缓存性能数据
            $cacheKey = 'performance_' . date('Y-m-d-H');
            $cachedData = Cache::get($cacheKey, []);
            $cachedData[] = $performanceData;
            Cache::set($cacheKey, $cachedData, 3600);
        });
    }
}
```

## 备份和恢复

### 1. 自动备份脚本
```bash
#!/bin/bash
# backup.sh

# 配置变量
BACKUP_DIR="/backup/likeshop"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 数据库备份
echo "开始数据库备份..."
mysqldump -u likeshop -p'password' \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  likeshop_prod | gzip > $BACKUP_DIR/database/db_backup_$DATE.sql.gz

# 文件备份
echo "开始文件备份..."
tar -czf $BACKUP_DIR/files/files_backup_$DATE.tar.gz \
  -C /var/www/likeshop/shared \
  uploads

# 配置文件备份
echo "开始配置文件备份..."
tar -czf $BACKUP_DIR/config/config_backup_$DATE.tar.gz \
  /var/www/likeshop/shared/.env \
  /etc/nginx/sites-available/likeshop \
  /etc/php/7.4/fpm/pool.d/likeshop.conf

# 清理过期备份
echo "清理过期备份..."
find $BACKUP_DIR/database -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR/files -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR/config -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "备份完成: $DATE"
```

### 2. 恢复脚本
```bash
#!/bin/bash
# restore.sh

if [ $# -ne 1 ]; then
    echo "使用方法: $0 <备份日期>"
    echo "示例: $0 20240115_143000"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/backup/likeshop"

echo "开始恢复 $BACKUP_DATE 的备份..."

# 恢复数据库
echo "恢复数据库..."
if [ -f "$BACKUP_DIR/database/db_backup_$BACKUP_DATE.sql.gz" ]; then
    zcat $BACKUP_DIR/database/db_backup_$BACKUP_DATE.sql.gz | mysql -u likeshop -p'password' likeshop_prod
    echo "数据库恢复完成"
else
    echo "数据库备份文件不存在"
    exit 1
fi

# 恢复文件
echo "恢复文件..."
if [ -f "$BACKUP_DIR/files/files_backup_$BACKUP_DATE.tar.gz" ]; then
    tar -xzf $BACKUP_DIR/files/files_backup_$BACKUP_DATE.tar.gz -C /var/www/likeshop/shared/
    chown -R deploy:deploy /var/www/likeshop/shared/uploads
    echo "文件恢复完成"
else
    echo "文件备份不存在"
fi

echo "恢复完成"
```

## 安全配置

### 1. 防火墙配置
```bash
#!/bin/bash
# firewall_setup.sh

# 安装并启用防火墙
yum install -y firewalld
systemctl start firewalld
systemctl enable firewalld

# 基础规则
firewall-cmd --permanent --add-service=ssh
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https

# 数据库端口（仅内网访问）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='10.0.0.0/8' port protocol='tcp' port='3306' accept"

# Redis端口（仅内网访问）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='10.0.0.0/8' port protocol='tcp' port='6379' accept"

# 迅排设计服务端口（仅内网访问）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='10.0.0.0/8' port protocol='tcp' port='7001' accept"

# 重载防火墙规则
firewall-cmd --reload

echo "防火墙配置完成"
```

### 2. SSL证书配置
```bash
#!/bin/bash
# ssl_setup.sh

DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# 安装Certbot
yum install -y certbot python3-certbot-nginx

# 获取SSL证书
certbot --nginx -d $DOMAIN -d www.$DOMAIN --email $EMAIL --agree-tos --non-interactive

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

echo "SSL证书配置完成"
```

### 3. 安全加固脚本
```bash
#!/bin/bash
# security_hardening.sh

# 禁用不必要的服务
systemctl disable telnet
systemctl disable rsh
systemctl disable rlogin

# 设置SSH安全配置
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config
systemctl restart sshd

# 安装fail2ban
yum install -y epel-release
yum install -y fail2ban

# 配置fail2ban
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = 2222

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
EOF

systemctl enable fail2ban
systemctl start fail2ban

echo "安全加固完成"
```

## 运维操作手册

### 1. 常用运维命令
```bash
# 查看应用状态
systemctl status nginx
systemctl status php7.4-fpm
systemctl status mysql
systemctl status redis

# 查看日志
tail -f /var/log/likeshop/error.log
tail -f /var/log/nginx/likeshop/error.log
tail -f /var/log/mysql/error.log

# 重启服务
systemctl restart php7.4-fpm
systemctl reload nginx
systemctl restart mysql

# 清理缓存
redis-cli -a 'your_redis_password' flushall
php /var/www/likeshop/current/think clear:cache

# 查看系统资源
htop
iotop
nethogs
```

### 2. 故障排查指南

#### 应用无法访问
```bash
# 检查Nginx状态
systemctl status nginx
nginx -t

# 检查PHP-FPM状态
systemctl status php7.4-fpm
tail -f /var/log/php/likeshop-error.log

# 检查端口监听
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

#### 数据库连接问题
```bash
# 检查MySQL状态
systemctl status mysql
mysql -u likeshop -p'password' -e "SELECT 1"

# 检查连接数
mysql -u root -p -e "SHOW STATUS LIKE 'Threads_connected'"
mysql -u root -p -e "SHOW PROCESSLIST"

# 检查慢查询
tail -f /var/log/mysql/slow.log
```

#### 性能问题排查
```bash
# 检查系统负载
uptime
top
iostat -x 1

# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查磁盘空间
df -h
du -sh /var/www/likeshop/*

# 检查网络连接
ss -tuln
netstat -i
```

### 3. 维护窗口操作
```bash
#!/bin/bash
# maintenance.sh

case "$1" in
    start)
        echo "开始维护模式..."
        # 创建维护页面
        cp /var/www/maintenance.html /var/www/likeshop/current/public/index.html
        # 停止队列处理
        supervisorctl stop likeshop-queue
        echo "维护模式已启用"
        ;;
    stop)
        echo "结束维护模式..."
        # 恢复正常页面
        rm -f /var/www/likeshop/current/public/index.html
        # 启动队列处理
        supervisorctl start likeshop-queue
        # 清理缓存
        php /var/www/likeshop/current/think clear:cache
        echo "维护模式已关闭"
        ;;
    *)
        echo "使用方法: $0 {start|stop}"
        exit 1
        ;;
esac
```

## 性能调优建议

### 1. MySQL优化
- 定期执行 `ANALYZE TABLE` 更新统计信息
- 监控慢查询日志，优化慢SQL
- 合理设置 `innodb_buffer_pool_size`
- 启用查询缓存

### 2. Redis优化
- 设置合理的内存淘汰策略
- 监控内存使用情况
- 定期清理过期键
- 使用Redis集群提高可用性

### 3. PHP优化
- 启用OPcache
- 调整PHP-FPM进程数
- 优化内存限制和执行时间
- 使用Composer优化自动加载

### 4. Nginx优化
- 启用Gzip压缩
- 配置静态文件缓存
- 使用HTTP/2协议
- 实施请求限流

## 联系信息

- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

---

**文档版本**: v1.0.0
**最后更新**: 2024-01-15
**维护团队**: 运维团队
```
```
