-- 模板商品化系统性能优化 - 数据库索引优化
-- 基于性能测试结果，针对性地创建和优化数据库索引

-- =====================================================
-- 1. 商品表索引优化
-- =====================================================

-- 商品类型和状态复合索引（用于商品列表查询）
CREATE INDEX idx_goods_type_status ON ls_goods (goods_type, status, create_time DESC);

-- 服务商品配置查询索引
CREATE INDEX idx_goods_service_config ON ls_goods (goods_type, service_config(100));

-- 商品名称搜索索引（全文索引）
ALTER TABLE ls_goods ADD FULLTEXT(name, description);

-- =====================================================
-- 2. 订单表索引优化
-- =====================================================

-- 用户订单查询索引
CREATE INDEX idx_order_user_type_status ON ls_order (user_id, order_type, order_status, create_time DESC);

-- 订单状态和支付状态复合索引
CREATE INDEX idx_order_status_pay ON ls_order (order_status, pay_status, create_time DESC);

-- 订单号唯一索引（如果不存在）
CREATE UNIQUE INDEX idx_order_sn ON ls_order (order_sn);

-- 服务订单特殊查询索引
CREATE INDEX idx_order_service_delivery ON ls_order (order_type, order_status, update_time DESC) 
WHERE order_type = 2;

-- =====================================================
-- 3. 团购活动表索引优化
-- =====================================================

-- 团购活动状态和时间索引
CREATE INDEX idx_group_buy_status_time ON ls_group_buy_activities (status, start_time, end_time);

-- 商品团购活动查询索引
CREATE INDEX idx_group_buy_goods_status ON ls_group_buy_activities (goods_id, status, created_at DESC);

-- 团购活动参与人数索引
CREATE INDEX idx_group_buy_participants ON ls_group_buy_activities (current_participants, min_participants, status);

-- =====================================================
-- 4. 参数填写会话表索引优化
-- =====================================================

-- 用户会话查询索引
CREATE INDEX idx_session_user_status ON ls_parameter_fill_sessions (user_id, is_completed, expires_at DESC);

-- 商品会话统计索引
CREATE INDEX idx_session_goods_completion ON ls_parameter_fill_sessions (goods_id, completion_rate, created_at DESC);

-- 过期会话清理索引
CREATE INDEX idx_session_expires ON ls_parameter_fill_sessions (expires_at, is_completed);

-- =====================================================
-- 5. 服务交付记录表索引优化
-- =====================================================

-- 订单交付状态查询索引
CREATE INDEX idx_delivery_order_status ON ls_service_delivery_records (order_id, status, updated_at DESC);

-- 用户交付记录查询索引
CREATE INDEX idx_delivery_user_status ON ls_service_delivery_records (user_id, status, created_at DESC);

-- 批量处理状态索引
CREATE INDEX idx_delivery_batch_status ON ls_service_delivery_records (status, created_at) 
WHERE status IN ('pending_generation', 'generated', 'printed');

-- 物流跟踪索引
CREATE INDEX idx_delivery_tracking ON ls_service_delivery_records (tracking_number, shipped_at) 
WHERE tracking_number IS NOT NULL;

-- =====================================================
-- 6. 订单参数表索引优化
-- =====================================================

-- 订单参数查询索引
CREATE INDEX idx_order_params_order ON ls_template_order_params (order_id, created_at DESC);

-- 模板参数统计索引
CREATE INDEX idx_order_params_template ON ls_template_order_params (template_config_id, created_at DESC);

-- =====================================================
-- 7. 用户地址表索引优化（如果存在）
-- =====================================================

-- 用户地址查询索引
CREATE INDEX idx_user_address_user_default ON ls_user_address (user_id, is_default DESC, del);

-- =====================================================
-- 8. 海报生成记录表索引优化（如果存在）
-- =====================================================

-- 生成状态和时间索引
CREATE INDEX idx_poster_generation_status ON ls_poster_generation_records (status, created_at DESC);

-- 订单生成记录索引
CREATE INDEX idx_poster_generation_order ON ls_poster_generation_records (order_id, status, generated_at DESC);

-- 批量生成任务索引
CREATE INDEX idx_poster_generation_batch ON ls_poster_generation_records (batch_id, status) 
WHERE batch_id IS NOT NULL;

-- =====================================================
-- 9. 系统日志表索引优化（如果存在）
-- =====================================================

-- 日志查询索引
CREATE INDEX idx_system_log_time_level ON ls_system_log (created_at DESC, log_level);

-- 用户操作日志索引
CREATE INDEX idx_system_log_user_action ON ls_system_log (user_id, action, created_at DESC);

-- =====================================================
-- 10. 性能监控相关索引
-- =====================================================

-- API调用记录索引（如果存在）
CREATE INDEX idx_api_log_endpoint_time ON ls_api_log (endpoint, response_time, created_at DESC);

-- 错误日志索引（如果存在）
CREATE INDEX idx_error_log_time_type ON ls_error_log (created_at DESC, error_type);

-- =====================================================
-- 索引使用情况分析查询
-- =====================================================

-- 查看索引使用情况
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     COLUMN_NAME,
--     CARDINALITY,
--     INDEX_TYPE
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = 'your_database_name'
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- 查看未使用的索引
-- SELECT 
--     s.TABLE_SCHEMA,
--     s.TABLE_NAME,
--     s.INDEX_NAME,
--     s.COLUMN_NAME
-- FROM INFORMATION_SCHEMA.STATISTICS s
-- LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage t 
--     ON s.TABLE_SCHEMA = t.OBJECT_SCHEMA 
--     AND s.TABLE_NAME = t.OBJECT_NAME 
--     AND s.INDEX_NAME = t.INDEX_NAME
-- WHERE t.INDEX_NAME IS NULL 
--     AND s.TABLE_SCHEMA = 'your_database_name'
--     AND s.INDEX_NAME != 'PRIMARY'
-- ORDER BY s.TABLE_NAME, s.INDEX_NAME;

-- =====================================================
-- 索引维护建议
-- =====================================================

-- 1. 定期分析表统计信息
-- ANALYZE TABLE ls_goods, ls_order, ls_group_buy_activities, 
--              ls_parameter_fill_sessions, ls_service_delivery_records;

-- 2. 定期优化表
-- OPTIMIZE TABLE ls_goods, ls_order, ls_group_buy_activities, 
--                ls_parameter_fill_sessions, ls_service_delivery_records;

-- 3. 监控慢查询日志
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;
-- SET GLOBAL log_queries_not_using_indexes = 'ON';

-- =====================================================
-- 注意事项
-- =====================================================

-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 创建索引可能需要较长时间，建议在业务低峰期执行
-- 3. 索引会占用额外的存储空间，需要监控磁盘使用情况
-- 4. 过多的索引会影响写入性能，需要平衡查询和写入性能
-- 5. 定期监控索引使用情况，删除不必要的索引

-- =====================================================
-- 执行顺序建议
-- =====================================================

-- 1. 先执行核心业务表的索引（商品、订单）
-- 2. 再执行扩展功能表的索引（团购、参数会话）
-- 3. 最后执行辅助表的索引（日志、监控）
-- 4. 执行完成后运行 ANALYZE TABLE 更新统计信息
