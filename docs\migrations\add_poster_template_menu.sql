-- 动态参数模板系统菜单数据插入
-- 创建时间: 2025-01-16
-- 描述: 为动态参数模板系统添加后台管理菜单

SET NAMES utf8mb4;

-- 获取当前最大的sort值，确保新菜单排在合适位置
SET @max_sort = (SELECT IFNULL(MAX(sort), 0) FROM ls_dev_auth WHERE pid = 0);

-- ============================================================================
-- 1. 添加主菜单：动态参数模板
-- ============================================================================
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(0, 1, 0, '动态参数模板', 'layui-icon-template-1', @max_sort + 10, '', 0, 0, UNIX_TIMESTAMP());

-- 获取刚插入的主菜单ID
SET @main_menu_id = LAST_INSERT_ID();

-- ============================================================================
-- 2. 添加子菜单
-- ============================================================================

-- 2.1 模板配置管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '模板配置', 'layui-icon-set', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP());

SET @config_menu_id = LAST_INSERT_ID();

-- 2.2 用户数据管理
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '用户数据', 'layui-icon-user', 90, 'poster_template/user_data_list', 0, 0, UNIX_TIMESTAMP());

SET @user_data_menu_id = LAST_INSERT_ID();

-- 2.3 生成记录
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '生成记录', 'layui-icon-chart', 80, 'poster_template/generation_records', 0, 0, UNIX_TIMESTAMP());

SET @records_menu_id = LAST_INSERT_ID();

-- 2.4 系统统计
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 1, 0, '系统统计', 'layui-icon-chart-screen', 70, 'poster_template/statistics', 0, 0, UNIX_TIMESTAMP());

SET @stats_menu_id = LAST_INSERT_ID();

-- ============================================================================
-- 3. 添加权限节点（type=2表示权限）
-- ============================================================================

-- 3.1 模板配置相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@config_menu_id, 2, 0, '查看配置列表', '', 100, 'poster_template/config_list', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '添加配置', '', 90, 'poster_template/config_add', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '编辑配置', '', 80, 'poster_template/config_edit', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '删除配置', '', 70, 'poster_template/config_delete', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '复制配置', '', 60, 'poster_template/config_copy', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '获取模板列表', '', 50, 'poster_template/get_templates', 0, 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, 0, '解析模板', '', 40, 'poster_template/parse_template', 0, 0, UNIX_TIMESTAMP());

-- 3.2 用户数据相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@user_data_menu_id, 2, 0, '查看用户数据', '', 100, 'poster_template/user_data_list', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '编辑用户数据', '', 90, 'poster_template/user_data_edit', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '删除用户数据', '', 80, 'poster_template/user_data_delete', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '生成预览', '', 70, 'poster_template/generate_preview', 0, 0, UNIX_TIMESTAMP()),
(@user_data_menu_id, 2, 0, '生成图片', '', 60, 'poster_template/generate_image', 0, 0, UNIX_TIMESTAMP());

-- 3.3 生成记录相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@records_menu_id, 2, 0, '查看生成记录', '', 100, 'poster_template/generation_records', 0, 0, UNIX_TIMESTAMP()),
(@records_menu_id, 2, 0, '删除生成记录', '', 90, 'poster_template/delete_generation_record', 0, 0, UNIX_TIMESTAMP()),
(@records_menu_id, 2, 0, '批量删除记录', '', 80, 'poster_template/batch_delete_records', 0, 0, UNIX_TIMESTAMP());

-- 3.4 统计相关权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@stats_menu_id, 2, 0, '查看统计数据', '', 100, 'poster_template/statistics', 0, 0, UNIX_TIMESTAMP()),
(@stats_menu_id, 2, 0, '获取统计信息', '', 90, 'poster_template/get_stats', 0, 0, UNIX_TIMESTAMP());

-- 3.5 外部API权限（供迅排设计调用）
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `sort`, `uri`, `disable`, `del`, `create_time`) VALUES
(@main_menu_id, 2, 0, '外部API-获取参数数据', '', 30, 'api/poster_external/get_parameter_data', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-获取参数配置', '', 20, 'api/poster_external/get_parameter_config', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-批量获取数据', '', 10, 'api/poster_external/batch_get_parameter_data', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-更新预览URL', '', 5, 'api/poster_external/update_preview_url', 0, 0, UNIX_TIMESTAMP()),
(@main_menu_id, 2, 0, '外部API-健康检查', '', 1, 'api/poster_external/health', 0, 0, UNIX_TIMESTAMP());

-- ============================================================================
-- 4. 清除菜单缓存（如果有缓存机制）
-- ============================================================================
-- 注意：这里可能需要根据实际的缓存清理机制来调整

-- ============================================================================
-- 5. 显示插入结果
-- ============================================================================
SELECT 'Poster Template System Menu Added Successfully!' as Status;

-- 显示插入的菜单结构
SELECT 
    id,
    pid,
    type,
    name,
    uri,
    CASE type 
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '权限'
        ELSE '未知'
    END as type_name
FROM ls_dev_auth 
WHERE (id = @main_menu_id OR pid = @main_menu_id OR pid IN (
    SELECT id FROM ls_dev_auth WHERE pid = @main_menu_id
))
ORDER BY pid, sort DESC;
