<?php
namespace app\api\controller;

class ApiBase {
    public $user_id = 1001;
    public $request;

    public function __construct($requestData = []) {
        $this->request = new \think\Request($requestData);
    }

    public function success($msg = "", $data = []) {
        return ["code" => 1, "msg" => $msg, "data" => $data];
    }

    public function fail($msg = "", $data = []) {
        return ["code" => 0, "msg" => $msg, "data" => $data];
    }
}