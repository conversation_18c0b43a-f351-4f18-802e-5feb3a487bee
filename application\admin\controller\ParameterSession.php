<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\logic\ParameterSessionLogic;
use think\Db;

class ParameterSession extends AdminBase
{
    /**
     * 参数会话列表页面
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $lists = $this->getSessionLists($get);
            return json($lists);
        }
        return $this->fetch();
    }

    /**
     * 获取会话列表数据
     * @param array $get
     * @return array
     */
    private function getSessionLists($get)
    {
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;
        
        // 构建查询条件
        $where = [];
        
        // 状态筛选
        if (!empty($get['status'])) {
            switch ($get['status']) {
                case 'active':
                    $where[] = ['is_completed', '=', 0];
                    $where[] = ['expires_at', '>', date('Y-m-d H:i:s')];
                    break;
                case 'completed':
                    $where[] = ['is_completed', '=', 1];
                    break;
                case 'expired':
                    $where[] = ['is_completed', '=', 0];
                    $where[] = ['expires_at', '<=', date('Y-m-d H:i:s')];
                    break;
            }
        }
        
        // 完成度筛选
        if (!empty($get['completion_rate'])) {
            switch ($get['completion_rate']) {
                case '0-25':
                    $where[] = ['completion_rate', 'between', [0, 25]];
                    break;
                case '26-50':
                    $where[] = ['completion_rate', 'between', [26, 50]];
                    break;
                case '51-75':
                    $where[] = ['completion_rate', 'between', [51, 75]];
                    break;
                case '76-100':
                    $where[] = ['completion_rate', 'between', [76, 100]];
                    break;
            }
        }
        
        // 时间范围筛选
        if (!empty($get['time_range'])) {
            $timeRange = explode(' - ', $get['time_range']);
            if (count($timeRange) == 2) {
                $where[] = ['created_at', 'between', [$timeRange[0] . ' 00:00:00', $timeRange[1] . ' 23:59:59']];
            }
        }
        
        // 关键词搜索
        if (!empty($get['search_keyword'])) {
            $keyword = trim($get['search_keyword']);
            $userIds = Db::name('user')
                ->where('nickname', 'like', "%{$keyword}%")
                ->whereOr('mobile', 'like', "%{$keyword}%")
                ->column('id');
            
            if (!empty($userIds)) {
                $where[] = ['user_id', 'in', $userIds];
            } else {
                // 如果没有找到匹配的用户，返回空结果
                $where[] = ['user_id', '=', 0];
            }
        }
        
        // 查询会话数据
        $sessions = Db::name('parameter_fill_sessions')
            ->alias('s')
            ->leftJoin('user u', 's.user_id = u.id')
            ->leftJoin('goods g', 's.goods_id = g.id')
            ->where($where)
            ->field('s.*, u.nickname, u.mobile, g.name as goods_name')
            ->order('s.created_at desc')
            ->paginate($limit, false, ['page' => $page]);
        
        $list = $sessions->items();
        
        // 处理数据
        foreach ($list as &$item) {
            // 计算剩余时间
            $remainingSeconds = strtotime($item['expires_at']) - time();
            $item['remaining_hours'] = max(0, round($remainingSeconds / 3600, 1));
            
            // 确定状态
            if ($item['is_completed']) {
                $item['status'] = 'completed';
                $item['status_text'] = '已完成';
            } elseif ($remainingSeconds <= 0) {
                $item['status'] = 'expired';
                $item['status_text'] = '已过期';
            } else {
                $item['status'] = 'active';
                $item['status_text'] = '进行中';
            }
        }
        
        // 获取统计数据
        $statistics = $this->getSessionStatistics($where);
        
        return [
            'code' => 0,
            'msg' => '',
            'count' => $sessions->total(),
            'data' => $list,
            'statistics' => $statistics
        ];
    }

    /**
     * 获取会话统计数据
     * @param array $where
     * @return array
     */
    private function getSessionStatistics($where = [])
    {
        $currentTime = date('Y-m-d H:i:s');
        
        // 总会话数
        $totalSessions = Db::name('parameter_fill_sessions')->where($where)->count();
        
        // 已完成会话数
        $completedSessions = Db::name('parameter_fill_sessions')
            ->where($where)
            ->where('is_completed', 1)
            ->count();
        
        // 进行中会话数
        $activeSessions = Db::name('parameter_fill_sessions')
            ->where($where)
            ->where([
                ['is_completed', '=', 0],
                ['expires_at', '>', $currentTime]
            ])
            ->count();
        
        // 已过期会话数
        $expiredSessions = Db::name('parameter_fill_sessions')
            ->where($where)
            ->where([
                ['is_completed', '=', 0],
                ['expires_at', '<=', $currentTime]
            ])
            ->count();
        
        return [
            'total_sessions' => $totalSessions,
            'completed_sessions' => $completedSessions,
            'active_sessions' => $activeSessions,
            'expired_sessions' => $expiredSessions
        ];
    }

    /**
     * 会话详情
     */
    public function detail()
    {
        $sessionId = $this->request->get('session_id');
        
        if (empty($sessionId)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 获取会话详情
        $session = Db::name('parameter_fill_sessions')
            ->alias('s')
            ->leftJoin('user u', 's.user_id = u.id')
            ->leftJoin('goods g', 's.goods_id = g.id')
            ->where('s.id', $sessionId)
            ->field('s.*, u.nickname, u.mobile, u.avatar, g.name as goods_name, g.image as goods_image, g.price as goods_price')
            ->find();
        
        if (!$session) {
            return json(['code' => 0, 'msg' => '会话不存在']);
        }
        
        // 计算剩余时间
        $remainingSeconds = strtotime($session['expires_at']) - time();
        $session['remaining_hours'] = max(0, round($remainingSeconds / 3600, 1));
        
        // 确定状态
        if ($session['is_completed']) {
            $session['status'] = 'completed';
            $session['status_text'] = '已完成';
        } elseif ($remainingSeconds <= 0) {
            $session['status'] = 'expired';
            $session['status_text'] = '已过期';
        } else {
            $session['status'] = 'active';
            $session['status_text'] = '进行中';
        }
        
        // 获取模板配置（模拟）
        $templateConfig = [
            'id' => $session['template_config_id'],
            'name' => '默认模板'
        ];
        
        // 解析参数值
        $parameterValues = json_decode($session['parameter_values'], true) ?: [];
        
        $data = [
            'session_info' => [
                'id' => $session['id'],
                'completion_rate' => $session['completion_rate'],
                'status' => $session['status'],
                'status_text' => $session['status_text'],
                'remaining_hours' => $session['remaining_hours'],
                'created_at' => $session['created_at'],
                'expires_at' => $session['expires_at']
            ],
            'user_info' => [
                'user_id' => $session['user_id'],
                'nickname' => $session['nickname'],
                'mobile' => $session['mobile'],
                'avatar' => $session['avatar']
            ],
            'goods_info' => [
                'id' => $session['goods_id'],
                'name' => $session['goods_name'],
                'image' => $session['goods_image'],
                'price' => $session['goods_price']
            ],
            'template_config' => $templateConfig,
            'parameter_values' => $parameterValues
        ];
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
    }

    /**
     * 延长会话过期时间
     */
    public function extend()
    {
        $sessionId = $this->request->post('session_id');
        $extendHours = $this->request->post('extend_hours');
        $remark = $this->request->post('remark', '');
        
        if (empty($sessionId) || empty($extendHours)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 检查会话是否存在
        $session = Db::name('parameter_fill_sessions')->where('id', $sessionId)->find();
        if (!$session) {
            return json(['code' => 0, 'msg' => '会话不存在']);
        }
        
        // 计算新的过期时间
        $newExpiresAt = date('Y-m-d H:i:s', strtotime($session['expires_at']) + $extendHours * 3600);
        
        // 更新会话
        $result = Db::name('parameter_fill_sessions')
            ->where('id', $sessionId)
            ->update([
                'expires_at' => $newExpiresAt,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            // 记录操作日志
            $this->recordOperationLog('延长会话', "会话ID: {$sessionId}, 延长时间: {$extendHours}小时, 备注: {$remark}");
            
            return json(['code' => 1, 'msg' => '延长成功']);
        } else {
            return json(['code' => 0, 'msg' => '延长失败']);
        }
    }

    /**
     * 重新激活会话
     */
    public function reactivate()
    {
        $sessionId = $this->request->post('session_id');
        
        if (empty($sessionId)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 检查会话是否存在
        $session = Db::name('parameter_fill_sessions')->where('id', $sessionId)->find();
        if (!$session) {
            return json(['code' => 0, 'msg' => '会话不存在']);
        }
        
        // 重新激活：延长24小时
        $newExpiresAt = date('Y-m-d H:i:s', time() + 24 * 3600);
        
        $result = Db::name('parameter_fill_sessions')
            ->where('id', $sessionId)
            ->update([
                'expires_at' => $newExpiresAt,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            // 记录操作日志
            $this->recordOperationLog('重新激活会话', "会话ID: {$sessionId}");
            
            return json(['code' => 1, 'msg' => '重新激活成功']);
        } else {
            return json(['code' => 0, 'msg' => '重新激活失败']);
        }
    }

    /**
     * 删除会话
     */
    public function delete()
    {
        $sessionId = $this->request->post('session_id');
        
        if (empty($sessionId)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 检查会话是否存在
        $session = Db::name('parameter_fill_sessions')->where('id', $sessionId)->find();
        if (!$session) {
            return json(['code' => 0, 'msg' => '会话不存在']);
        }
        
        // 检查是否可以删除（已完成的会话不能删除）
        if ($session['is_completed']) {
            return json(['code' => 0, 'msg' => '已完成的会话不能删除']);
        }
        
        $result = Db::name('parameter_fill_sessions')->where('id', $sessionId)->delete();
        
        if ($result) {
            // 记录操作日志
            $this->recordOperationLog('删除会话', "会话ID: {$sessionId}");
            
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '删除失败']);
        }
    }

    /**
     * 处理异常会话
     */
    public function processAbnormal()
    {
        $result = ParameterSessionLogic::processExpiredSessions();
        
        if ($result['success']) {
            // 记录操作日志
            $this->recordOperationLog('处理异常会话', "处理数量: {$result['data']['expired_count']}");
            
            return json(['code' => 1, 'msg' => '处理完成', 'data' => $result['data']]);
        } else {
            return json(['code' => 0, 'msg' => $result['error']]);
        }
    }

    /**
     * 记录操作日志
     * @param string $action
     * @param string $content
     */
    private function recordOperationLog($action, $content)
    {
        // 这里可以记录到操作日志表
        // 暂时省略具体实现
    }
}
