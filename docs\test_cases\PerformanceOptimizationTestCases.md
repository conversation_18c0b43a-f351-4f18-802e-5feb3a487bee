# 模板商品化系统性能优化测试用例

## 测试目标
对模板商品化系统进行全面的性能测试，识别性能瓶颈并制定优化方案，确保系统在预期负载下能够稳定运行，提供良好的用户体验。

## 测试环境要求
- 完整的模板商品化系统已部署
- 性能测试工具已配置（Apache Bench, JMeter等）
- 数据库监控工具已启用
- 系统监控工具已配置
- 测试数据已准备完成

## 性能指标要求

### 基础性能指标
- **API响应时间**: < 500ms (95%请求)
- **数据库查询时间**: < 100ms (单次查询)
- **页面加载时间**: < 3秒 (首屏加载)
- **系统可用性**: > 99.5%
- **并发处理能力**: 支持100个并发用户
- **海报生成成功率**: > 95%

### 业务性能指标
- **转化率**: 从浏览到购买 > 5%
- **团购成团率**: > 80%
- **订单处理时间**: < 2秒
- **文件下载速度**: > 1MB/s

## 性能测试用例

### PT001: 数据库查询性能测试
**测试目的**: 验证核心数据库查询的性能表现
**测试类型**: 数据库性能测试
**预计执行时间**: 60分钟

#### 测试场景1: 商品查询性能
**测试数据**:
- 商品总数: 10,000条
- 服务商品: 1,000条
- 团购活动: 500个

**测试SQL**:
```sql
-- 1. 商品列表查询（带筛选）
SELECT * FROM ls_goods 
WHERE goods_type = 2 AND status = 1 
ORDER BY create_time DESC 
LIMIT 20;

-- 2. 团购活动查询
SELECT g.*, gb.* FROM ls_goods g
LEFT JOIN ls_group_buy_activities gb ON g.id = gb.goods_id
WHERE g.goods_type = 2 AND gb.status = 'active'
ORDER BY gb.created_at DESC;

-- 3. 参数会话查询
SELECT s.*, u.nickname, g.name as goods_name
FROM ls_parameter_fill_sessions s
LEFT JOIN ls_user u ON s.user_id = u.id
LEFT JOIN ls_goods g ON s.goods_id = g.id
WHERE s.user_id = ? AND s.is_completed = 0;
```

**性能要求**:
- 单次查询时间 < 50ms
- 复杂关联查询 < 100ms
- 分页查询 < 80ms

**预期结果**:
- 所有查询在性能要求范围内
- 索引使用率 > 90%
- 无全表扫描

#### 测试场景2: 写入操作性能
**测试操作**:
```sql
-- 1. 订单创建（事务操作）
BEGIN;
INSERT INTO ls_order (...) VALUES (...);
INSERT INTO ls_template_order_params (...) VALUES (...);
INSERT INTO ls_service_delivery_records (...) VALUES (...);
UPDATE ls_parameter_fill_sessions SET is_completed = 1 WHERE id = ?;
COMMIT;

-- 2. 批量数据插入
INSERT INTO ls_poster_generation_records 
(order_id, template_id, parameters, status, created_at) 
VALUES 
(1, 'template_001', '{}', 'pending', NOW()),
(2, 'template_002', '{}', 'pending', NOW()),
...;
```

**性能要求**:
- 单次事务操作 < 200ms
- 批量插入(100条) < 500ms
- 并发写入无死锁

### PT002: API接口性能测试
**测试目的**: 验证核心API接口的响应性能
**测试类型**: 接口性能测试
**预计执行时间**: 90分钟

#### 测试场景1: 参数填写API性能
**测试接口**:
- `POST /api/parameter_session/create` - 创建参数会话
- `PUT /api/parameter_session/{id}` - 更新参数值
- `POST /api/parameter_session/{id}/preview` - 生成预览
- `GET /api/parameter_session/{id}` - 获取会话详情

**测试配置**:
```bash
# Apache Bench 测试命令
ab -n 1000 -c 10 -H "Authorization: Bearer test_token" \
   -p create_session.json -T application/json \
   http://localhost/api/parameter_session/create

ab -n 1000 -c 20 -H "Authorization: Bearer test_token" \
   http://localhost/api/parameter_session/123
```

**性能要求**:
- 平均响应时间 < 300ms
- 95%请求响应时间 < 500ms
- 99%请求响应时间 < 1000ms
- 错误率 < 1%

#### 测试场景2: 服务订单API性能
**测试接口**:
- `POST /api/service_order/create` - 创建服务订单
- `GET /api/service_order/{id}/status` - 获取订单状态
- `GET /api/service_order/{id}/delivery` - 获取交付信息
- `GET /api/service_order/lists` - 获取订单列表

**负载测试配置**:
```yaml
# JMeter 测试计划
ThreadGroup:
  threads: 50
  ramp_up: 30s
  duration: 300s
  
HTTPRequest:
  - name: CreateOrder
    method: POST
    path: /api/service_order/create
    body: order_data.json
    
  - name: GetOrderStatus
    method: GET
    path: /api/service_order/${order_id}/status
    
  - name: GetOrderList
    method: GET
    path: /api/service_order/lists?page=1&limit=20
```

**性能要求**:
- 订单创建 < 800ms (包含数据库事务)
- 状态查询 < 200ms
- 列表查询 < 400ms
- 并发50用户无性能衰减

### PT003: 团购功能性能测试
**测试目的**: 验证团购功能在高并发情况下的性能表现
**测试类型**: 并发性能测试
**预计执行时间**: 120分钟

#### 测试场景1: 团购参与并发测试
**测试描述**: 模拟多用户同时参与团购活动
**并发配置**:
- 并发用户数: 100
- 测试时长: 10分钟
- 团购活动: 最小成团人数3人

**测试脚本**:
```javascript
// JMeter JavaScript 测试脚本
var activityId = vars.get("activity_id");
var userId = vars.get("user_id");

// 参与团购请求
var joinData = {
    "activity_id": activityId,
    "user_id": userId,
    "parameters": {
        "title": "用户" + userId + "的海报",
        "subtitle": "个性化定制"
    }
};

// 发送请求并记录响应时间
var startTime = System.currentTimeMillis();
var response = httpRequest("POST", "/api/group_buy/join", joinData);
var endTime = System.currentTimeMillis();
var responseTime = endTime - startTime;

// 验证响应
if (response.code === 1) {
    log.info("团购参与成功，响应时间: " + responseTime + "ms");
} else {
    log.error("团购参与失败: " + response.msg);
}
```

**性能要求**:
- 团购参与响应时间 < 600ms
- 成团检查响应时间 < 300ms
- 数据一致性100%（无重复参与）
- 并发处理成功率 > 98%

#### 测试场景2: 团购成团处理性能
**测试描述**: 验证团购成团后的批量处理性能
**测试数据**:
- 团购活动数: 50个
- 每个活动参与人数: 10人
- 总订单数: 500个

**批量处理测试**:
```php
// 批量海报生成性能测试
$activities = getGroupedActivities(); // 获取已成团活动

$startTime = microtime(true);
foreach ($activities as $activity) {
    $result = ServiceDeliveryLogic::batchGeneratePosters($activity['id']);
    if (!$result['success']) {
        echo "批量生成失败: " . $result['error'] . "\n";
    }
}
$endTime = microtime(true);
$totalTime = $endTime - $startTime;

echo "批量处理总时间: " . round($totalTime, 2) . "秒\n";
echo "平均每个活动处理时间: " . round($totalTime / count($activities), 2) . "秒\n";
```

**性能要求**:
- 单个团购成团处理 < 5秒
- 批量海报生成 < 30秒/100张
- 内存使用 < 512MB
- CPU使用率 < 80%

### PT004: 海报生成性能测试
**测试目的**: 验证海报生成服务的性能和稳定性
**测试类型**: 外部服务性能测试
**预计执行时间**: 90分钟

#### 测试场景1: 单个海报生成性能
**测试配置**:
```php
// 海报生成性能测试
$testCases = [
    ['template_id' => 'template_001', 'complexity' => 'simple'],
    ['template_id' => 'template_002', 'complexity' => 'medium'],
    ['template_id' => 'template_003', 'complexity' => 'complex']
];

foreach ($testCases as $case) {
    $startTime = microtime(true);
    
    $result = PosterTemplateService::generatePoster(
        $case['template_id'],
        ['title' => '测试海报', 'subtitle' => '性能测试'],
        ['width' => 1242, 'height' => 2208, 'quality' => 0.9]
    );
    
    $endTime = microtime(true);
    $duration = $endTime - $startTime;
    
    echo "模板 {$case['template_id']} 生成时间: " . round($duration, 2) . "秒\n";
    echo "复杂度: {$case['complexity']}, 成功: " . ($result['success'] ? '是' : '否') . "\n";
}
```

**性能要求**:
- 简单模板生成 < 3秒
- 中等复杂度模板 < 8秒
- 复杂模板生成 < 15秒
- 生成成功率 > 95%

#### 测试场景2: 批量海报生成性能
**测试描述**: 模拟团购成团后的批量生成场景
**测试数据**:
- 批量大小: 10, 50, 100张
- 模板类型: 混合复杂度
- 并发批次: 5个

**批量生成测试**:
```php
// 批量生成性能测试
$batchSizes = [10, 50, 100];
$concurrentBatches = 5;

foreach ($batchSizes as $batchSize) {
    echo "测试批量大小: $batchSize\n";
    
    $startTime = microtime(true);
    
    // 模拟并发批量生成
    $processes = [];
    for ($i = 0; $i < $concurrentBatches; $i++) {
        $processes[] = startBatchGeneration($batchSize);
    }
    
    // 等待所有批次完成
    waitForAllProcesses($processes);
    
    $endTime = microtime(true);
    $totalTime = $endTime - $startTime;
    
    echo "批量大小 $batchSize，并发 $concurrentBatches 批次\n";
    echo "总时间: " . round($totalTime, 2) . "秒\n";
    echo "平均每张: " . round($totalTime / ($batchSize * $concurrentBatches), 2) . "秒\n";
}
```

**性能要求**:
- 批量10张 < 30秒
- 批量50张 < 120秒
- 批量100张 < 200秒
- 并发处理无性能衰减

### PT005: 系统资源使用测试
**测试目的**: 监控系统资源使用情况，识别资源瓶颈
**测试类型**: 系统监控测试
**预计执行时间**: 180分钟

#### 测试场景1: 内存使用监控
**监控指标**:
- PHP进程内存使用
- 数据库连接池使用
- 缓存内存使用
- 系统总内存使用

**监控脚本**:
```bash
#!/bin/bash
# 系统资源监控脚本

echo "开始系统资源监控..."
echo "时间,CPU使用率,内存使用率,磁盘IO,网络IO,数据库连接数" > performance_log.csv

for i in {1..360}; do  # 监控6小时
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    memory_usage=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')
    disk_io=$(iostat -x 1 1 | grep -E "(sda|nvme)" | awk '{print $10}')
    network_io=$(cat /proc/net/dev | grep eth0 | awk '{print $2+$10}')
    db_connections=$(mysql -e "SHOW STATUS LIKE 'Threads_connected';" | tail -1 | awk '{print $2}')
    
    echo "$timestamp,$cpu_usage,$memory_usage,$disk_io,$network_io,$db_connections" >> performance_log.csv
    
    sleep 60  # 每分钟记录一次
done
```

**性能要求**:
- CPU使用率 < 70% (平均)
- 内存使用率 < 80%
- 磁盘IO等待 < 10%
- 数据库连接数 < 100

#### 测试场景2: 数据库性能监控
**监控SQL**:
```sql
-- 慢查询监控
SELECT 
    query_time,
    lock_time,
    rows_sent,
    rows_examined,
    sql_text
FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY query_time DESC;

-- 连接状态监控
SHOW STATUS LIKE 'Threads_%';
SHOW STATUS LIKE 'Connection%';

-- 缓存命中率监控
SHOW STATUS LIKE 'Qcache%';
```

**性能要求**:
- 慢查询数量 < 10/小时
- 查询缓存命中率 > 80%
- 连接使用率 < 80%
- 锁等待时间 < 1秒

### PT006: 压力测试和极限测试
**测试目的**: 确定系统的性能极限和稳定性边界
**测试类型**: 压力测试
**预计执行时间**: 240分钟

#### 测试场景1: 逐步加压测试
**测试配置**:
```yaml
# 逐步加压测试配置
StepLoad:
  initial_users: 10
  step_size: 10
  step_duration: 300s  # 5分钟
  max_users: 200
  
Scenarios:
  - name: "混合业务场景"
    weight: 100%
    requests:
      - browse_goods: 40%
      - create_session: 20%
      - update_parameters: 15%
      - generate_preview: 10%
      - create_order: 10%
      - join_group_buy: 5%
```

**监控指标**:
- 响应时间变化趋势
- 错误率变化趋势
- 系统资源使用变化
- 数据库性能变化

#### 测试场景2: 峰值负载测试
**测试描述**: 模拟系统峰值使用场景
**负载配置**:
- 并发用户: 500
- 持续时间: 30分钟
- 业务场景: 团购活动开始时的高并发

**预期结果**:
- 系统保持稳定运行
- 响应时间增长 < 50%
- 错误率 < 5%
- 无系统崩溃

## 测试执行计划

### 测试阶段安排
1. **第1天**: 数据库查询性能测试 (PT001)
2. **第2天**: API接口性能测试 (PT002)
3. **第3天**: 团购功能性能测试 (PT003)
4. **第4天**: 海报生成性能测试 (PT004)
5. **第5天**: 系统资源监控测试 (PT005)
6. **第6-7天**: 压力测试和极限测试 (PT006)

### 测试环境配置
- **硬件配置**: 4核CPU, 8GB内存, SSD硬盘
- **软件环境**: PHP 7.4, MySQL 5.7, Redis 6.0
- **网络环境**: 千兆网络，低延迟
- **监控工具**: Grafana, Prometheus, MySQL监控

### 测试数据准备
- **用户数据**: 10,000个测试用户
- **商品数据**: 1,000个服务商品
- **模板数据**: 100个海报模板
- **历史数据**: 50,000条历史订单

## 性能优化方向

### 数据库优化
1. **索引优化**: 添加复合索引，优化查询性能
2. **查询优化**: 重写慢查询，减少数据传输
3. **连接池优化**: 调整连接池大小和超时设置
4. **分区表**: 对大数据量表进行分区

### 应用层优化
1. **缓存策略**: 实现多级缓存机制
2. **异步处理**: 耗时操作改为异步处理
3. **批量操作**: 减少数据库连接次数
4. **代码优化**: 优化算法和数据结构

### 系统架构优化
1. **负载均衡**: 实现应用服务器负载均衡
2. **读写分离**: 数据库读写分离
3. **CDN加速**: 静态资源CDN分发
4. **微服务拆分**: 核心服务独立部署

## 成功标准
- 所有性能指标达到要求
- 系统在预期负载下稳定运行
- 用户体验满足业务需求
- 资源使用合理，成本可控

## 测试工具和脚本

### 性能测试工具
- **Apache Bench (ab)**: 简单的HTTP负载测试
- **JMeter**: 复杂场景的性能测试
- **MySQL Performance Schema**: 数据库性能监控
- **htop/top**: 系统资源监控
- **iostat**: 磁盘IO监控

### 自动化测试脚本
- **数据库性能测试脚本**: `performance_db_test.php`
- **API性能测试脚本**: `performance_api_test.sh`
- **系统监控脚本**: `system_monitor.sh`
- **压力测试配置**: `load_test.jmx`

### 测试报告模板
- **性能测试报告**: 包含所有测试结果和分析
- **优化建议报告**: 基于测试结果的优化方案
- **监控仪表板**: 实时性能监控界面
