<?php
/**
 * 性能优化功能验证测试脚本
 * 验证性能优化实现的功能完整性和有效性
 */

echo "开始验证性能优化功能实现\n";
echo "====================================\n";

// 测试结果统计
$test_results = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0
];

function log_test_result($test_name, $success, $message = '', $details = []) {
    global $test_results;
    
    $test_results['total']++;
    
    if ($success) {
        $test_results['passed']++;
        echo "✓ PASS: $test_name - $message\n";
    } else {
        $test_results['failed']++;
        echo "✗ FAIL: $test_name - $message\n";
        
        if (!empty($details)) {
            foreach ($details as $detail) {
                echo "  - $detail\n";
            }
        }
    }
}

/**
 * 测试数据库索引优化脚本
 */
function test_database_index_optimization() {
    echo "\n=== 测试数据库索引优化 ===\n";
    
    $indexFile = __DIR__ . '/../sql/performance_optimization_indexes.sql';
    $exists = file_exists($indexFile);
    log_test_result('索引优化脚本', $exists, $exists ? '文件存在' : '文件缺失');
    
    if ($exists) {
        $content = file_get_contents($indexFile);
        
        // 检查核心索引
        $requiredIndexes = [
            'idx_goods_type_status' => '商品类型状态索引',
            'idx_order_user_type_status' => '用户订单索引',
            'idx_group_buy_status_time' => '团购状态时间索引',
            'idx_session_user_status' => '用户会话索引',
            'idx_delivery_order_status' => '交付状态索引'
        ];
        
        foreach ($requiredIndexes as $index => $desc) {
            $hasIndex = strpos($content, $index) !== false;
            log_test_result('索引检查', $hasIndex, $hasIndex ? "$desc 存在" : "$desc 缺失");
        }
        
        // 检查索引类型
        $indexTypes = ['CREATE INDEX', 'CREATE UNIQUE INDEX', 'FULLTEXT'];
        foreach ($indexTypes as $type) {
            $hasType = strpos($content, $type) !== false;
            log_test_result('索引类型', $hasType, $hasType ? "$type 索引存在" : "$type 索引缺失");
        }
        
        // 检查维护建议
        $maintenanceItems = ['ANALYZE TABLE', 'OPTIMIZE TABLE', 'slow_query_log'];
        foreach ($maintenanceItems as $item) {
            $hasItem = strpos($content, $item) !== false;
            log_test_result('维护建议', $hasItem, $hasItem ? "$item 建议存在" : "$item 建议缺失");
        }
    }
}

/**
 * 测试性能优化服务类
 */
function test_performance_optimization_service() {
    echo "\n=== 测试性能优化服务类 ===\n";
    
    $serviceFile = __DIR__ . '/../../application/common/server/PerformanceOptimizationService.php';
    $exists = file_exists($serviceFile);
    log_test_result('性能优化服务', $exists, $exists ? '文件存在' : '文件缺失');
    
    if ($exists) {
        $content = file_get_contents($serviceFile);
        
        // 检查核心方法
        $requiredMethods = [
            'getCachedGoodsList' => '缓存商品列表',
            'getCachedGroupBuyList' => '缓存团购列表',
            'getCachedTemplateConfig' => '缓存模板配置',
            'getBatchOrderStatus' => '批量订单状态',
            'clearRelatedCache' => '清除相关缓存',
            'monitorQueryPerformance' => '查询性能监控',
            'monitorApiPerformance' => 'API性能监控',
            'healthCheck' => '健康检查'
        ];
        
        foreach ($requiredMethods as $method => $desc) {
            $hasMethod = strpos($content, "function $method") !== false;
            log_test_result('服务方法', $hasMethod, $hasMethod ? "$desc 方法存在" : "$desc 方法缺失");
        }
        
        // 检查缓存配置
        $cacheFeatures = [
            'CACHE_PREFIX' => '缓存前缀',
            'CACHE_EXPIRE' => '缓存过期配置',
            'Cache::get' => '缓存读取',
            'Cache::set' => '缓存写入',
            'Cache::clear' => '缓存清理'
        ];
        
        foreach ($cacheFeatures as $feature => $desc) {
            $hasFeature = strpos($content, $feature) !== false;
            log_test_result('缓存功能', $hasFeature, $hasFeature ? "$desc 存在" : "$desc 缺失");
        }
        
        // 检查监控功能
        $monitorFeatures = [
            'Log::warning' => '警告日志',
            'Log::error' => '错误日志',
            'microtime' => '时间测量',
            'memory_get_usage' => '内存监控'
        ];
        
        foreach ($monitorFeatures as $feature => $desc) {
            $hasFeature = strpos($content, $feature) !== false;
            log_test_result('监控功能', $hasFeature, $hasFeature ? "$desc 存在" : "$desc 缺失");
        }
    }
}

/**
 * 测试性能监控中间件
 */
function test_performance_monitor_middleware() {
    echo "\n=== 测试性能监控中间件 ===\n";
    
    $middlewareFile = __DIR__ . '/../../application/common/middleware/PerformanceMonitor.php';
    $exists = file_exists($middlewareFile);
    log_test_result('性能监控中间件', $exists, $exists ? '文件存在' : '文件缺失');
    
    if ($exists) {
        $content = file_get_contents($middlewareFile);
        
        // 检查中间件结构
        $middlewareStructure = [
            'class PerformanceMonitor' => '中间件类定义',
            'function handle' => '处理方法',
            'microtime(true)' => '时间测量',
            'memory_get_usage()' => '内存测量',
            'getEndpoint' => '端点提取',
            'recordPerformanceData' => '性能数据记录'
        ];
        
        foreach ($middlewareStructure as $structure => $desc) {
            $hasStructure = strpos($content, $structure) !== false;
            log_test_result('中间件结构', $hasStructure, $hasStructure ? "$desc 存在" : "$desc 缺失");
        }
        
        // 检查监控指标
        $monitorMetrics = [
            'response_time' => '响应时间',
            'memory_usage' => '内存使用',
            'status_code' => '状态码',
            'X-Response-Time' => '响应时间头',
            'X-Memory-Usage' => '内存使用头'
        ];
        
        foreach ($monitorMetrics as $metric => $desc) {
            $hasMetric = strpos($content, $metric) !== false;
            log_test_result('监控指标', $hasMetric, $hasMetric ? "$desc 存在" : "$desc 缺失");
        }
        
        // 检查统计功能
        $statsFeatures = [
            'updatePerformanceStats' => '统计更新',
            'getPerformanceReport' => '性能报告',
            'total_requests' => '请求总数',
            'error_count' => '错误计数',
            'slow_request_count' => '慢请求计数'
        ];
        
        foreach ($statsFeatures as $feature => $desc) {
            $hasFeature = strpos($content, $feature) !== false;
            log_test_result('统计功能', $hasFeature, $hasFeature ? "$desc 存在" : "$desc 缺失");
        }
    }
}

/**
 * 测试性能优化配置
 */
function test_performance_optimization_config() {
    echo "\n=== 测试性能优化配置 ===\n";
    
    // 检查是否有性能相关的配置文件
    $configFiles = [
        __DIR__ . '/../../config/cache.php' => '缓存配置',
        __DIR__ . '/../../config/database.php' => '数据库配置',
        __DIR__ . '/../../config/log.php' => '日志配置'
    ];
    
    foreach ($configFiles as $file => $desc) {
        $exists = file_exists($file);
        log_test_result('配置文件', $exists, $exists ? "$desc 存在" : "$desc 缺失");
        
        if ($exists) {
            $content = file_get_contents($file);
            
            // 检查缓存配置
            if (strpos($file, 'cache.php') !== false) {
                $cacheTypes = ['redis', 'file', 'memcache'];
                $hasCacheType = false;
                foreach ($cacheTypes as $type) {
                    if (strpos($content, $type) !== false) {
                        $hasCacheType = true;
                        break;
                    }
                }
                log_test_result('缓存类型', $hasCacheType, $hasCacheType ? '缓存类型配置存在' : '缓存类型配置缺失');
            }
            
            // 检查数据库配置
            if (strpos($file, 'database.php') !== false) {
                $dbFeatures = ['charset', 'prefix', 'debug'];
                foreach ($dbFeatures as $feature) {
                    $hasFeature = strpos($content, $feature) !== false;
                    log_test_result('数据库配置', $hasFeature, $hasFeature ? "$feature 配置存在" : "$feature 配置缺失");
                }
            }
        }
    }
}

/**
 * 测试性能优化工具脚本
 */
function test_performance_tools() {
    echo "\n=== 测试性能优化工具 ===\n";
    
    // 检查是否有性能测试和监控脚本
    $toolFiles = [
        __DIR__ . '/performance_optimization_test_script.php' => '性能测试脚本',
        __DIR__ . '/../sql/performance_optimization_indexes.sql' => '索引优化脚本'
    ];
    
    foreach ($toolFiles as $file => $desc) {
        $exists = file_exists($file);
        log_test_result('工具脚本', $exists, $exists ? "$desc 存在" : "$desc 缺失");
        
        if ($exists) {
            $content = file_get_contents($file);
            $size = strlen($content);
            $hasContent = $size > 1000; // 至少1KB内容
            log_test_result('脚本内容', $hasContent, $hasContent ? "$desc 内容充实($size bytes)" : "$desc 内容不足($size bytes)");
        }
    }
}

/**
 * 测试性能优化文档
 */
function test_performance_documentation() {
    echo "\n=== 测试性能优化文档 ===\n";
    
    $docFiles = [
        __DIR__ . '/PerformanceOptimizationTestCases.md' => '性能测试用例文档',
        __DIR__ . '/business_flow_test_report.md' => '业务流程测试报告'
    ];
    
    foreach ($docFiles as $file => $desc) {
        $exists = file_exists($file);
        log_test_result('文档文件', $exists, $exists ? "$desc 存在" : "$desc 缺失");
        
        if ($exists) {
            $content = file_get_contents($file);
            
            // 检查文档结构
            $docStructures = ['##', '###', '```', '**', '*'];
            $structureCount = 0;
            foreach ($docStructures as $structure) {
                if (strpos($content, $structure) !== false) {
                    $structureCount++;
                }
            }
            
            $hasGoodStructure = $structureCount >= 3;
            log_test_result('文档结构', $hasGoodStructure, $hasGoodStructure ? "$desc 结构完整" : "$desc 结构简单");
            
            // 检查内容长度
            $contentLength = strlen($content);
            $hasRichContent = $contentLength > 5000; // 至少5KB内容
            log_test_result('文档内容', $hasRichContent, $hasRichContent ? "$desc 内容丰富($contentLength chars)" : "$desc 内容较少($contentLength chars)");
        }
    }
}

/**
 * 测试性能优化集成度
 */
function test_performance_integration() {
    echo "\n=== 测试性能优化集成度 ===\n";
    
    // 检查性能优化在系统中的集成情况
    $integrationChecks = [
        'PerformanceOptimizationService' => '性能优化服务集成',
        'PerformanceMonitor' => '性能监控中间件集成',
        'performance_optimization_indexes.sql' => '数据库索引优化集成'
    ];
    
    $integrationScore = 0;
    foreach ($integrationChecks as $component => $desc) {
        // 检查组件是否存在
        $componentExists = false;
        
        if (strpos($component, '.php') !== false || strpos($component, 'Service') !== false) {
            $file = __DIR__ . '/../../application/common/server/' . $component . '.php';
            if (!file_exists($file)) {
                $file = __DIR__ . '/../../application/common/middleware/' . $component . '.php';
            }
            $componentExists = file_exists($file);
        } elseif (strpos($component, '.sql') !== false) {
            $file = __DIR__ . '/../sql/' . $component;
            $componentExists = file_exists($file);
        }
        
        if ($componentExists) {
            $integrationScore++;
        }
        
        log_test_result('集成检查', $componentExists, $componentExists ? "$desc 已集成" : "$desc 未集成");
    }
    
    $integrationRate = round(($integrationScore / count($integrationChecks)) * 100, 1);
    log_test_result('集成度评估', $integrationRate >= 80, "性能优化集成度: $integrationRate%");
}

// 执行所有测试
test_database_index_optimization();
test_performance_optimization_service();
test_performance_monitor_middleware();
test_performance_optimization_config();
test_performance_tools();
test_performance_documentation();
test_performance_integration();

// 输出测试结果统计
echo "\n====================================\n";
echo "性能优化功能验证完成\n";
echo "总计: {$test_results['total']} 个测试项\n";
echo "通过: {$test_results['passed']} 个测试项\n";
echo "失败: {$test_results['failed']} 个测试项\n";

$success_rate = round(($test_results['passed'] / $test_results['total']) * 100, 1);
echo "验证通过率: $success_rate%\n";

if ($success_rate >= 90) {
    echo "✓ 性能优化功能实现完整！系统性能优化已准备就绪。\n";
    exit(0);
} elseif ($success_rate >= 80) {
    echo "? 性能优化功能基本完整，但需要完善部分功能。\n";
    exit(1);
} else {
    echo "✗ 性能优化功能实现不完整，需要进一步开发。\n";
    exit(2);
}
?>
