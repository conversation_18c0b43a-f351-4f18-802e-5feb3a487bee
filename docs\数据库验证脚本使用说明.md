# 数据库验证脚本使用说明

## 概述
为了简化数据库结构验证过程，我们提供了两个自动化验证脚本，可以自动检查模板商品化系统的数据库迁移状态和结构完整性，无需人工逐项检查。

## 脚本文件

### 1. PHP验证脚本
- **文件**: `docs/database_validation_script.php`
- **适用环境**: 有PHP环境的服务器或开发机
- **特点**: 功能完整，报告详细，支持Web访问

### 2. Shell验证脚本  
- **文件**: `docs/database_validation.sh`
- **适用环境**: Linux/Unix服务器
- **特点**: 轻量级，执行快速，适合服务器环境

## 使用方法

### PHP脚本使用

#### 方法一：命令行执行（推荐）
```bash
# 在项目根目录执行
php docs/database_validation_script.php
```

#### 方法二：Web浏览器访问
```
http://your-domain/docs/database_validation_script.php
```

#### 配置数据库连接
编辑 `database_validation_script.php` 文件开头的配置：
```php
$config = [
    'host' => 'localhost',        // 数据库主机
    'port' => 3306,              // 数据库端口
    'database' => 'likeshop',    // 数据库名称
    'username' => 'root',        // 数据库用户名
    'password' => '',            // 数据库密码
    'charset' => 'utf8mb4'       // 字符集
];
```

### Shell脚本使用

#### 基本使用
```bash
# 基本验证
bash docs/database_validation.sh

# 显示详细报告
bash docs/database_validation.sh --detailed

# 显示帮助信息
bash docs/database_validation.sh --help
```

#### 配置数据库连接
编辑 `database_validation.sh` 文件开头的配置：
```bash
DB_HOST="localhost"     # 数据库主机
DB_PORT="3306"         # 数据库端口  
DB_NAME="likeshop"     # 数据库名称
DB_USER="root"         # 数据库用户名
DB_PASS=""             # 数据库密码
```

#### 设置执行权限
```bash
chmod +x docs/database_validation.sh
```

## 验证内容

### 1. 商品表扩展验证
检查 `ls_goods` 表是否正确添加了以下字段：
- `goods_type` - 商品类型
- `template_config_id` - 模板配置ID
- `service_delivery_time` - 服务交付时间
- `print_materials` - 打印材质
- `print_sizes` - 打印尺寸
- `support_group_buy` - 是否支持团购
- `group_buy_min_count` - 团购最小人数
- `group_buy_price` - 团购价格
- `group_buy_duration` - 团购时长

### 2. 新核心表验证
检查以下新表是否正确创建：
- `ls_parameter_fill_sessions` - 参数填写会话表
- `ls_template_order_params` - 模板订单参数表
- `ls_service_delivery_records` - 服务交付记录表
- `ls_group_buy_activities` - 团购活动表
- `ls_group_buy_participants` - 团购参与记录表
- `ls_file_download_logs` - 文件下载日志表

### 3. 表结构完整性验证
检查每个新表的必需字段是否存在：
- 主键字段
- 外键字段
- 核心业务字段
- 时间戳字段

### 4. 索引和约束验证
检查以下索引和约束：
- 商品表的索引（`idx_goods_type`, `idx_template_config`）
- 外键约束设置
- 主键约束

### 5. 数据完整性验证
检查数据的逻辑一致性：
- 服务商品必须关联模板配置
- 团购活动最小人数必须大于0
- 参数会话过期时间不能早于创建时间

## 验证结果解读

### 通过率评级
- **95%以上**: 优秀 - 数据库结构完整，可以正常使用
- **80-94%**: 良好 - 存在少量问题，建议修复后使用
- **60-79%**: 一般 - 存在较多问题，需要修复后使用
- **60%以下**: 不合格 - 存在严重问题，不建议使用

### 结果状态说明
- **PASS** ✓ - 检查通过
- **FAIL** ✗ - 检查失败，需要修复
- **WARN** ⚠ - 警告，建议关注但不影响核心功能

### 常见问题及解决方案

#### 问题1：数据库连接失败
**现象**: 脚本提示数据库连接失败
**解决方案**:
1. 检查数据库服务是否启动
2. 验证数据库连接配置是否正确
3. 确认数据库用户权限是否足够

#### 问题2：表或字段不存在
**现象**: 脚本提示某些表或字段不存在
**解决方案**:
1. 检查是否执行了数据库迁移脚本
2. 运行 `docs/模板商品化项目文档/COMPLETE_DATABASE_MIGRATION.sql`
3. 确认数据库名称是否正确

#### 问题3：权限不足
**现象**: 脚本提示权限错误
**解决方案**:
1. 确认数据库用户具有SELECT权限
2. 确认数据库用户具有访问information_schema的权限
3. 使用具有足够权限的数据库用户

#### 问题4：字符集问题
**现象**: 中文显示乱码
**解决方案**:
1. 确认数据库字符集为utf8mb4
2. 确认终端支持UTF-8编码
3. 检查数据库连接字符集配置

## 集成到CI/CD

### 在部署脚本中使用
```bash
#!/bin/bash
# 部署脚本示例

echo "开始部署模板商品化系统..."

# 执行数据库验证
echo "验证数据库结构..."
if bash docs/database_validation.sh; then
    echo "数据库验证通过，继续部署..."
else
    echo "数据库验证失败，停止部署！"
    exit 1
fi

# 继续其他部署步骤...
```

### 在自动化测试中使用
```php
// PHPUnit测试示例
public function testDatabaseStructure()
{
    $output = shell_exec('php docs/database_validation_script.php');
    $this->assertStringContains('优秀', $output);
}
```

## 定期维护

### 建议执行频率
- **开发环境**: 每次数据库变更后
- **测试环境**: 每次部署前
- **生产环境**: 每月定期检查

### 日志记录
脚本执行结果可以重定向到日志文件：
```bash
# 记录验证日志
bash docs/database_validation.sh --detailed > validation_$(date +%Y%m%d_%H%M%S).log 2>&1
```

### 监控告警
可以将验证脚本集成到监控系统中：
```bash
# 验证失败时发送告警
if ! bash docs/database_validation.sh; then
    echo "数据库结构验证失败" | mail -s "系统告警" <EMAIL>
fi
```

## 总结

使用自动化验证脚本可以：
1. **提高效率**: 几秒钟完成全面验证，替代人工逐项检查
2. **减少错误**: 避免人工检查遗漏或错误
3. **标准化**: 确保验证过程的一致性和可重复性
4. **便于集成**: 可以集成到部署流程和自动化测试中
5. **详细报告**: 提供清晰的验证结果和问题定位

建议在系统部署、升级、维护等关键节点都执行数据库验证，确保系统的稳定性和可靠性。
