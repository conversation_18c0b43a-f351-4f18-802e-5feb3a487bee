<?php
/**
 * 部署文档完整性验证测试脚本
 * 验证部署文档的完整性、准确性和可操作性
 */

echo "开始验证部署文档完整性\n";
echo "====================================\n";

// 测试结果统计
$test_results = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0
];

function log_test_result($test_name, $success, $message = '', $details = []) {
    global $test_results;
    
    $test_results['total']++;
    
    if ($success) {
        $test_results['passed']++;
        echo "✓ PASS: $test_name - $message\n";
    } else {
        $test_results['failed']++;
        echo "✗ FAIL: $test_name - $message\n";
        
        if (!empty($details)) {
            foreach ($details as $detail) {
                echo "  - $detail\n";
            }
        }
    }
}

/**
 * 测试部署文档文件存在性
 */
function test_deployment_documentation_files() {
    echo "\n=== 测试部署文档文件 ===\n";
    
    $docFiles = [
        __DIR__ . '/../deployment/模板商品化系统部署运维指南.md' => '部署运维指南',
        __DIR__ . '/../deployment/database_migration_script.sh' => '数据库迁移脚本'
    ];
    
    foreach ($docFiles as $file => $desc) {
        $exists = file_exists($file);
        log_test_result('文档文件', $exists, $exists ? "$desc 存在" : "$desc 缺失");
        
        if ($exists) {
            $content = file_get_contents($file);
            $size = strlen($content);
            $hasContent = $size > 10000; // 至少10KB内容
            log_test_result('文档内容', $hasContent, $hasContent ? "$desc 内容充实($size bytes)" : "$desc 内容不足($size bytes)");
        }
    }
}

/**
 * 测试部署文档结构完整性
 */
function test_deployment_documentation_structure() {
    echo "\n=== 测试部署文档结构 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('文档结构', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查文档基本结构
    $basicStructures = [
        '# 模板商品化系统部署运维指南' => '文档标题',
        '## 概述' => '概述章节',
        '## 系统架构' => '系统架构章节',
        '## 环境要求' => '环境要求章节',
        '## 部署前准备' => '部署前准备章节',
        '## 数据库迁移' => '数据库迁移章节',
        '## 应用部署' => '应用部署章节',
        '## 外部服务配置' => '外部服务配置章节',
        '## 监控和日志' => '监控日志章节',
        '## 备份和恢复' => '备份恢复章节',
        '## 安全配置' => '安全配置章节',
        '## 运维操作手册' => '运维手册章节'
    ];
    
    foreach ($basicStructures as $structure => $desc) {
        $hasStructure = strpos($content, $structure) !== false;
        log_test_result('文档结构', $hasStructure, $hasStructure ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查技术栈说明
    $techStack = [
        'ThinkPHP' => 'PHP框架',
        'MySQL' => '数据库',
        'Redis' => '缓存',
        'Nginx' => 'Web服务器',
        'PHP 7.4' => 'PHP版本'
    ];
    
    foreach ($techStack as $tech => $desc) {
        $hasTech = strpos($content, $tech) !== false;
        log_test_result('技术栈', $hasTech, $hasTech ? "$desc 说明存在" : "$desc 说明缺失");
    }
}

/**
 * 测试部署脚本完整性
 */
function test_deployment_scripts_completeness() {
    echo "\n=== 测试部署脚本完整性 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('脚本完整性', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查部署脚本
    $deploymentScripts = [
        'deploy.sh' => '应用部署脚本',
        'backup.sh' => '备份脚本',
        'restore.sh' => '恢复脚本',
        'health_check.sh' => '健康检查脚本',
        'performance_monitor.sh' => '性能监控脚本',
        'maintenance.sh' => '维护模式脚本'
    ];
    
    foreach ($deploymentScripts as $script => $desc) {
        $hasScript = strpos($content, $script) !== false;
        log_test_result('部署脚本', $hasScript, $hasScript ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查配置文件
    $configFiles = [
        'mysqld.cnf' => 'MySQL配置',
        'redis.conf' => 'Redis配置',
        'nginx' => 'Nginx配置',
        'php-fpm' => 'PHP-FPM配置',
        '.env' => '环境配置'
    ];
    
    foreach ($configFiles as $config => $desc) {
        $hasConfig = strpos($content, $config) !== false;
        log_test_result('配置文件', $hasConfig, $hasConfig ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查Docker配置
    $dockerConfigs = [
        'docker-compose.yml' => 'Docker Compose配置',
        'Dockerfile' => 'Docker镜像配置',
        'volumes:' => 'Docker卷配置',
        'networks:' => 'Docker网络配置'
    ];
    
    foreach ($dockerConfigs as $docker => $desc) {
        $hasDocker = strpos($content, $docker) !== false;
        log_test_result('Docker配置', $hasDocker, $hasDocker ? "$desc 存在" : "$desc 缺失");
    }
}

/**
 * 测试数据库迁移脚本
 */
function test_database_migration_script() {
    echo "\n=== 测试数据库迁移脚本 ===\n";
    
    $scriptFile = __DIR__ . '/../deployment/database_migration_script.sh';
    if (!file_exists($scriptFile)) {
        log_test_result('迁移脚本', false, '数据库迁移脚本不存在');
        return;
    }
    
    $content = file_get_contents($scriptFile);
    
    // 检查脚本基本结构
    $scriptStructures = [
        '#!/bin/bash' => 'Bash脚本头',
        'set -e' => '错误处理',
        'log()' => '日志函数',
        'check_dependencies()' => '依赖检查函数',
        'check_database_connection()' => '数据库连接检查',
        'create_backup()' => '备份创建函数',
        'execute_migration()' => '迁移执行函数',
        'verify_migration()' => '迁移验证函数',
        'rollback_migration()' => '回滚函数'
    ];
    
    foreach ($scriptStructures as $structure => $desc) {
        $hasStructure = strpos($content, $structure) !== false;
        log_test_result('脚本结构', $hasStructure, $hasStructure ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查命令行参数支持
    $cliOptions = [
        '--help' => '帮助选项',
        '--backup-only' => '仅备份选项',
        '--rollback' => '回滚选项',
        '--verify-only' => '仅验证选项',
        '--dry-run' => '预演选项'
    ];
    
    foreach ($cliOptions as $option => $desc) {
        $hasOption = strpos($content, $option) !== false;
        log_test_result('命令行选项', $hasOption, $hasOption ? "$desc 支持" : "$desc 不支持");
    }
    
    // 检查环境变量支持
    $envVars = [
        'DB_HOST' => '数据库主机',
        'DB_PORT' => '数据库端口',
        'DB_NAME' => '数据库名称',
        'DB_USER' => '数据库用户',
        'DB_PASS' => '数据库密码'
    ];
    
    foreach ($envVars as $var => $desc) {
        $hasVar = strpos($content, $var) !== false;
        log_test_result('环境变量', $hasVar, $hasVar ? "$desc 支持" : "$desc 不支持");
    }
    
    // 检查脚本权限
    $isExecutable = is_executable($scriptFile);
    log_test_result('脚本权限', $isExecutable, $isExecutable ? '脚本可执行' : '脚本不可执行');
}

/**
 * 测试监控和运维配置
 */
function test_monitoring_and_operations() {
    echo "\n=== 测试监控和运维配置 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('监控配置', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查监控配置
    $monitoringConfigs = [
        'supervisor' => 'Supervisor进程管理',
        'logrotate' => '日志轮转',
        'performance_monitor.sh' => '性能监控脚本',
        'health_check.sh' => '健康检查脚本',
        'fail2ban' => '安全防护'
    ];
    
    foreach ($monitoringConfigs as $config => $desc) {
        $hasConfig = strpos($content, $config) !== false;
        log_test_result('监控配置', $hasConfig, $hasConfig ? "$desc 配置存在" : "$desc 配置缺失");
    }
    
    // 检查日志管理
    $logManagement = [
        '/var/log/likeshop' => '应用日志目录',
        '/var/log/nginx' => 'Nginx日志目录',
        '/var/log/mysql' => 'MySQL日志目录',
        'tail -f' => '日志查看命令',
        'logrotate' => '日志轮转配置'
    ];
    
    foreach ($logManagement as $log => $desc) {
        $hasLog = strpos($content, $log) !== false;
        log_test_result('日志管理', $hasLog, $hasLog ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查备份策略
    $backupStrategy = [
        'mysqldump' => '数据库备份',
        'tar -czf' => '文件备份',
        'RETENTION_DAYS' => '备份保留策略',
        'crontab' => '定时备份',
        'gzip' => '备份压缩'
    ];
    
    foreach ($backupStrategy as $backup => $desc) {
        $hasBackup = strpos($content, $backup) !== false;
        log_test_result('备份策略', $hasBackup, $hasBackup ? "$desc 配置存在" : "$desc 配置缺失");
    }
}

/**
 * 测试安全配置
 */
function test_security_configuration() {
    echo "\n=== 测试安全配置 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('安全配置', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查防火墙配置
    $firewallConfigs = [
        'firewalld' => '防火墙服务',
        'firewall-cmd' => '防火墙命令',
        '--permanent' => '永久规则',
        '--add-service=ssh' => 'SSH服务',
        '--add-service=http' => 'HTTP服务',
        '--add-service=https' => 'HTTPS服务'
    ];
    
    foreach ($firewallConfigs as $config => $desc) {
        $hasConfig = strpos($content, $config) !== false;
        log_test_result('防火墙配置', $hasConfig, $hasConfig ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查SSL配置
    $sslConfigs = [
        'ssl_certificate' => 'SSL证书配置',
        'ssl_certificate_key' => 'SSL私钥配置',
        'ssl_protocols' => 'SSL协议配置',
        'certbot' => 'SSL证书自动化',
        'TLSv1.2' => 'TLS版本配置'
    ];
    
    foreach ($sslConfigs as $config => $desc) {
        $hasConfig = strpos($content, $config) !== false;
        log_test_result('SSL配置', $hasConfig, $hasConfig ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查安全加固
    $securityHardening = [
        'PermitRootLogin no' => '禁用root登录',
        'PasswordAuthentication no' => '禁用密码认证',
        'fail2ban' => '入侵防护',
        'X-Frame-Options' => '安全头配置',
        'X-Content-Type-Options' => '内容类型保护'
    ];
    
    foreach ($securityHardening as $hardening => $desc) {
        $hasHardening = strpos($content, $hardening) !== false;
        log_test_result('安全加固', $hasHardening, $hasHardening ? "$desc 配置存在" : "$desc 配置缺失");
    }
}

/**
 * 测试故障排查指南
 */
function test_troubleshooting_guide() {
    echo "\n=== 测试故障排查指南 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('故障排查', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查故障排查章节
    $troubleshootingSections = [
        '故障排查指南' => '故障排查章节',
        '应用无法访问' => '应用访问问题',
        '数据库连接问题' => '数据库问题',
        '性能问题排查' => '性能问题'
    ];
    
    foreach ($troubleshootingSections as $section => $desc) {
        $hasSection = strpos($content, $section) !== false;
        log_test_result('故障排查', $hasSection, $hasSection ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查常用运维命令
    $operationCommands = [
        'systemctl status' => '服务状态检查',
        'tail -f' => '日志查看',
        'systemctl restart' => '服务重启',
        'htop' => '系统监控',
        'netstat -tlnp' => '端口检查'
    ];
    
    foreach ($operationCommands as $command => $desc) {
        $hasCommand = strpos($content, $command) !== false;
        log_test_result('运维命令', $hasCommand, $hasCommand ? "$desc 命令存在" : "$desc 命令缺失");
    }
    
    // 检查性能调优建议
    $performanceTuning = [
        'MySQL优化' => 'MySQL性能优化',
        'Redis优化' => 'Redis性能优化',
        'PHP优化' => 'PHP性能优化',
        'Nginx优化' => 'Nginx性能优化'
    ];
    
    foreach ($performanceTuning as $tuning => $desc) {
        $hasTuning = strpos($content, $tuning) !== false;
        log_test_result('性能调优', $hasTuning, $hasTuning ? "$desc 建议存在" : "$desc 建议缺失");
    }
}

/**
 * 测试文档质量和可操作性
 */
function test_documentation_quality() {
    echo "\n=== 测试文档质量 ===\n";
    
    $docFile = __DIR__ . '/../deployment/模板商品化系统部署运维指南.md';
    if (!file_exists($docFile)) {
        log_test_result('文档质量', false, '部署文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查代码块数量
    $codeBlockCount = substr_count($content, '```');
    $hasEnoughCodeBlocks = $codeBlockCount >= 30;
    log_test_result('代码示例', $hasEnoughCodeBlocks, $hasEnoughCodeBlocks ? "包含$codeBlockCount个代码块" : "代码示例不足($codeBlockCount个)");
    
    // 检查配置示例
    $configExamples = [
        '```bash' => 'Bash脚本示例',
        '```ini' => '配置文件示例',
        '```sql' => 'SQL脚本示例',
        '```yaml' => 'YAML配置示例',
        '```nginx' => 'Nginx配置示例'
    ];
    
    foreach ($configExamples as $example => $desc) {
        $hasExample = strpos($content, $example) !== false;
        log_test_result('配置示例', $hasExample, $hasExample ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查文档结构
    $structureElements = [
        '##' => '二级标题',
        '###' => '三级标题',
        '####' => '四级标题',
        '- **' => '重点列表',
        '| ' => '表格内容'
    ];
    
    foreach ($structureElements as $element => $desc) {
        $elementCount = substr_count($content, $element);
        $hasElement = $elementCount > 0;
        log_test_result('文档结构', $hasElement, $hasElement ? "$desc ($elementCount个)" : "$desc 缺失");
    }
    
    // 检查联系信息
    $contactInfo = [
        '联系信息' => '联系信息章节',
        'tech-support@' => '技术支持邮箱',
        'ops@' => '运维团队邮箱',
        '文档版本' => '版本信息',
        '最后更新' => '更新时间'
    ];
    
    foreach ($contactInfo as $info => $desc) {
        $hasInfo = strpos($content, $info) !== false;
        log_test_result('文档信息', $hasInfo, $hasInfo ? "$desc 存在" : "$desc 缺失");
    }
}

// 执行所有测试
test_deployment_documentation_files();
test_deployment_documentation_structure();
test_deployment_scripts_completeness();
test_database_migration_script();
test_monitoring_and_operations();
test_security_configuration();
test_troubleshooting_guide();
test_documentation_quality();

// 输出测试结果统计
echo "\n====================================\n";
echo "部署文档验证完成\n";
echo "总计: {$test_results['total']} 个测试项\n";
echo "通过: {$test_results['passed']} 个测试项\n";
echo "失败: {$test_results['failed']} 个测试项\n";

$success_rate = round(($test_results['passed'] / $test_results['total']) * 100, 1);
echo "验证通过率: $success_rate%\n";

if ($success_rate >= 95) {
    echo "✓ 部署文档质量优秀！文档完整、详细、可操作。\n";
    exit(0);
} elseif ($success_rate >= 85) {
    echo "? 部署文档质量良好，但需要完善部分内容。\n";
    exit(1);
} else {
    echo "✗ 部署文档质量需要改进，请完善缺失的内容。\n";
    exit(2);
}
?>
