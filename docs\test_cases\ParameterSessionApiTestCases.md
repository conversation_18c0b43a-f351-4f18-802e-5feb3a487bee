# 参数填写API接口测试用例

## 测试目标
验证移动端参数填写API接口的完整功能，包括会话创建、参数更新、预览生成、订单创建等核心业务流程的正确性和稳定性。

## 测试环境要求
- API服务正常运行
- 参数会话管理逻辑类已实现
- 数据库中存在测试用的商品和用户数据
- PosterTemplateService服务正常运行
- 用户认证系统正常工作

## 测试用例

### TC001: 创建参数填写会话 - 正常流程
**测试目的**: 验证用户创建参数填写会话的正常流程
**接口路径**: `POST /api/parameter_session/create`
**前置条件**: 
- 用户已登录
- 商品为有效的服务商品
- 用户没有该商品的未完成会话

**测试数据**:
```json
{
  "goods_id": 123,
  "user_id": 1001,
  "headers": {
    "Authorization": "Bearer valid_token",
    "Content-Type": "application/json"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "会话创建成功",
  "data": {
    "session_id": "session_20240115_001",
    "expires_at": "2024-01-16 10:30:00",
    "template_config": {
      "id": "template_001",
      "name": "商务海报模板",
      "parameter_schema": {
        "title": {"type": "text", "required": true, "max_length": 50},
        "subtitle": {"type": "text", "required": false, "max_length": 100},
        "background_color": {"type": "color", "required": false, "default": "#ffffff"}
      }
    },
    "parameter_values": {},
    "completion_rate": 0
  }
}
```

**执行步骤**:
1. 发送POST请求到创建会话接口
2. 验证响应状态码为200
3. 验证响应数据结构正确
4. 验证会话ID格式正确
5. 验证过期时间设置合理

**预期结果**:
- 响应状态码为200
- 返回有效的会话ID
- 模板配置信息完整
- 过期时间为24小时后
- 数据库中正确创建会话记录

### TC002: 创建参数填写会话 - 已有未完成会话
**测试目的**: 验证用户已有未完成会话时的处理逻辑
**接口路径**: `POST /api/parameter_session/create`
**前置条件**: 
- 用户已登录
- 用户已有该商品的未完成会话

**测试数据**:
```json
{
  "goods_id": 123,
  "user_id": 1001,
  "existing_session": {
    "id": "session_existing_001",
    "expires_at": "2024-01-15 20:30:00",
    "parameter_values": {"title": "已填写的标题"},
    "completion_rate": 33.3
  }
}
```

**执行步骤**:
1. 发送POST请求到创建会话接口
2. 验证返回现有会话信息
3. 验证过期时间已延长
4. 验证现有参数值保持不变

**预期结果**:
- 返回现有会话ID
- 过期时间延长到24小时后
- 现有参数值和完成度保持不变
- 不创建新的会话记录

### TC003: 更新参数值 - 部分参数更新
**测试目的**: 验证参数值的部分更新功能
**接口路径**: `PUT /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 存在有效的参数填写会话

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "parameter_values": {
    "title": "我的专属海报",
    "background_color": "#ff6b6b"
  },
  "headers": {
    "Authorization": "Bearer valid_token",
    "Content-Type": "application/json"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "参数更新成功",
  "data": {
    "completion_rate": 66.7,
    "can_submit": false,
    "parameter_values": {
      "title": "我的专属海报",
      "background_color": "#ff6b6b"
    },
    "validation_errors": []
  }
}
```

**执行步骤**:
1. 发送PUT请求更新参数值
2. 验证参数值正确更新
3. 验证完成度正确计算
4. 验证提交权限正确判断

**预期结果**:
- 参数值成功更新
- 完成度正确计算（2/3必填参数）
- can_submit为false（缺少必填参数）
- 无验证错误

### TC004: 更新参数值 - 完整参数更新
**测试目的**: 验证所有必填参数填写完成时的处理逻辑
**接口路径**: `PUT /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 存在有效的参数填写会话

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "parameter_values": {
    "title": "我的专属海报",
    "subtitle": "专业定制服务",
    "background_color": "#ff6b6b"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "参数更新成功",
  "data": {
    "completion_rate": 100.0,
    "can_submit": true,
    "parameter_values": {
      "title": "我的专属海报",
      "subtitle": "专业定制服务",
      "background_color": "#ff6b6b"
    },
    "validation_errors": []
  }
}
```

**执行步骤**:
1. 发送PUT请求更新所有参数
2. 验证完成度为100%
3. 验证can_submit为true
4. 验证所有参数值正确保存

**预期结果**:
- 完成度为100%
- can_submit为true
- 所有参数值正确保存
- 可以进行下一步操作

### TC005: 参数验证 - 格式错误
**测试目的**: 验证参数格式验证的正确性
**接口路径**: `PUT /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 存在有效的参数填写会话

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "parameter_values": {
    "title": "这是一个超过50个字符限制的非常长的标题文本内容，用于测试长度验证功能",
    "background_color": "invalid_color_format"
  }
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "参数验证失败",
  "data": {
    "validation_errors": [
      "title超过最大长度50",
      "background_color颜色格式错误"
    ]
  }
}
```

**执行步骤**:
1. 发送包含格式错误参数的请求
2. 验证返回验证错误信息
3. 验证参数值未被更新
4. 验证错误信息准确描述问题

**预期结果**:
- 返回验证失败响应
- 错误信息准确描述问题
- 参数值未被更新
- 会话状态保持不变

### TC006: 生成预览 - 正常流程
**测试目的**: 验证预览生成功能的正常流程
**接口路径**: `POST /api/parameter_session/{session_id}/preview`
**前置条件**: 
- 用户已登录
- 存在有效的参数填写会话
- 会话有足够的参数值用于预览

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "preview_options": {
    "width": 1242,
    "height": 2208,
    "quality": 0.9
  },
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "预览生成成功",
  "data": {
    "preview_url": "https://example.com/preview/session_20240115_001.jpg",
    "preview_expires_at": "2024-01-15 12:30:00",
    "preview_id": "preview_001",
    "generation_time": 2.5
  }
}
```

**执行步骤**:
1. 发送POST请求生成预览
2. 验证预览URL有效
3. 验证预览过期时间合理
4. 验证生成时间记录

**预期结果**:
- 成功生成预览图片
- 返回有效的预览URL
- 预览过期时间设置合理
- 生成时间在合理范围内

### TC007: 生成预览 - 参数不足
**测试目的**: 验证参数不足时预览生成的处理逻辑
**接口路径**: `POST /api/parameter_session/{session_id}/preview`
**前置条件**: 
- 用户已登录
- 存在参数不完整的会话

**测试数据**:
```json
{
  "session_id": "session_incomplete_001",
  "current_parameters": {
    "background_color": "#ffffff"
    // 缺少必填的title参数
  }
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "参数不完整，无法生成预览",
  "data": {
    "missing_required_parameters": ["title"],
    "completion_rate": 33.3,
    "can_preview": false
  }
}
```

**执行步骤**:
1. 发送预览生成请求
2. 验证返回参数不足错误
3. 验证缺失参数列表正确
4. 验证完成度计算正确

**预期结果**:
- 返回参数不足错误
- 明确指出缺失的必填参数
- 完成度计算正确
- 不生成预览图片

### TC008: 获取会话详情
**测试目的**: 验证获取会话详情的功能
**接口路径**: `GET /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 存在有效的参数填写会话

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "session_id": "session_20240115_001",
    "goods_info": {
      "id": 123,
      "name": "海报定制服务",
      "price": 29.90,
      "image": "/uploads/goods/poster.jpg"
    },
    "template_config": {
      "id": "template_001",
      "name": "商务海报模板",
      "parameter_schema": {...}
    },
    "parameter_values": {
      "title": "我的专属海报",
      "background_color": "#ff6b6b"
    },
    "completion_rate": 66.7,
    "can_submit": false,
    "expires_at": "2024-01-16 10:30:00",
    "remaining_hours": 18.5
  }
}
```

**执行步骤**:
1. 发送GET请求获取会话详情
2. 验证返回数据完整性
3. 验证剩余时间计算正确
4. 验证权限验证正确

**预期结果**:
- 返回完整的会话信息
- 商品和模板信息正确
- 参数值和完成度准确
- 剩余时间计算正确

### TC009: 完成填写并创建订单
**测试目的**: 验证完成参数填写并创建订单的功能
**接口路径**: `POST /api/parameter_session/{session_id}/complete`
**前置条件**: 
- 用户已登录
- 会话参数填写完整
- 用户有足够余额或选择了支付方式

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "order_info": {
    "consignee": "张三",
    "mobile": "13800138000",
    "address": "北京市朝阳区xxx街道xxx号",
    "payment_method": "wechat",
    "remark": "请尽快处理"
  },
  "headers": {
    "Authorization": "Bearer valid_token"
  }
}
```

**预期响应**:
```json
{
  "code": 1,
  "msg": "订单创建成功",
  "data": {
    "order_id": "LS202401150001",
    "order_sn": "LS202401150001",
    "total_amount": 29.90,
    "payment_info": {
      "payment_method": "wechat",
      "payment_url": "weixin://wxpay/...",
      "expires_at": "2024-01-15 11:00:00"
    },
    "delivery_info": {
      "estimated_delivery_time": "3-5个工作日",
      "delivery_address": "北京市朝阳区xxx街道xxx号"
    }
  }
}
```

**执行步骤**:
1. 发送POST请求完成订单创建
2. 验证订单信息正确
3. 验证支付信息生成
4. 验证会话状态更新为已完成

**预期结果**:
- 成功创建订单
- 支付信息正确生成
- 配送信息正确设置
- 会话标记为已完成

### TC010: 权限验证 - 无效token
**测试目的**: 验证API接口的权限验证功能
**接口路径**: `POST /api/parameter_session/create`
**前置条件**: 
- 使用无效或过期的token

**测试数据**:
```json
{
  "goods_id": 123,
  "headers": {
    "Authorization": "Bearer invalid_token",
    "Content-Type": "application/json"
  }
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "用户未登录或token已过期",
  "data": null
}
```

**执行步骤**:
1. 使用无效token发送请求
2. 验证返回权限错误
3. 验证错误信息准确
4. 验证不执行业务逻辑

**预期结果**:
- 返回权限验证失败
- 错误信息明确
- 不执行任何业务操作
- 响应状态码为401

### TC011: 会话过期处理
**测试目的**: 验证过期会话的处理逻辑
**接口路径**: `PUT /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 会话已过期

**测试数据**:
```json
{
  "session_id": "session_expired_001",
  "parameter_values": {
    "title": "尝试更新过期会话"
  },
  "session_expires_at": "2024-01-14 10:30:00" // 已过期
}
```

**预期响应**:
```json
{
  "code": 0,
  "msg": "会话已过期，请重新创建",
  "data": {
    "expired_at": "2024-01-14 10:30:00",
    "can_recreate": true
  }
}
```

**执行步骤**:
1. 尝试更新过期会话
2. 验证返回过期错误
3. 验证提示重新创建
4. 验证不更新任何数据

**预期结果**:
- 返回会话过期错误
- 提示用户重新创建会话
- 不执行参数更新操作
- 提供重新创建的引导

### TC012: 并发访问处理
**测试目的**: 验证同一会话的并发访问处理
**接口路径**: `PUT /api/parameter_session/{session_id}`
**前置条件**: 
- 用户已登录
- 同时发送多个更新请求

**测试数据**:
```json
{
  "session_id": "session_20240115_001",
  "concurrent_requests": [
    {"parameter_values": {"title": "标题1"}},
    {"parameter_values": {"title": "标题2"}},
    {"parameter_values": {"title": "标题3"}}
  ]
}
```

**执行步骤**:
1. 同时发送多个参数更新请求
2. 验证数据一致性
3. 验证最终状态正确
4. 验证无数据丢失

**预期结果**:
- 所有请求都能正确处理
- 最终数据状态一致
- 无数据竞争问题
- 完成度计算正确

## 测试方法说明

### API测试验证方法
1. **接口功能测试**: 验证每个API接口的基本功能和业务逻辑
2. **参数验证测试**: 测试各种有效和无效的参数组合
3. **权限验证测试**: 测试用户身份验证和权限控制
4. **异常场景测试**: 测试网络异常、服务异常等边界情况

### 集成测试验证方法
1. **端到端流程测试**: 从会话创建到订单完成的完整流程
2. **服务依赖测试**: 验证与PosterTemplateService等外部服务的集成
3. **数据一致性测试**: 验证数据库操作的一致性和完整性

### 性能测试验证方法
1. **响应时间测试**: 验证API接口的响应时间
2. **并发测试**: 测试高并发情况下的系统稳定性
3. **负载测试**: 验证系统在预期负载下的性能表现

### 测试执行顺序
1. 先测试基础接口功能（TC001、TC002、TC008）
2. 再测试核心业务流程（TC003、TC004、TC006、TC009）
3. 然后测试异常和边界情况（TC005、TC007、TC010、TC011、TC012）

## 成功标准
- 所有API接口功能正常
- 参数验证逻辑准确
- 权限控制严格有效
- 异常处理机制可靠
- 数据一致性得到保证
- 性能满足预期要求
- 与现有系统集成无冲突
