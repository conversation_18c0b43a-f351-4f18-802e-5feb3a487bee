<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\admin\logic\ParameterSessionLogic;
use app\admin\logic\ServiceDeliveryLogic;
use app\api\logic\OrderLogic;
use think\Db;

class ServiceOrder extends ApiBase
{
    public $like_not_need_login = [];

    /**
     * 创建服务订单
     */
    public function create()
    {
        $session_id = $this->request->post('session_id');
        $address_id = $this->request->post('address_id');
        $print_options = $this->request->post('print_options', []);
        $payment_method = $this->request->post('payment_method', 'wechat');
        $remark = $this->request->post('remark', '');
        
        if (empty($session_id)) {
            return $this->_error('参数填写会话ID不能为空');
        }
        
        if (empty($address_id)) {
            return $this->_error('收货地址不能为空');
        }
        
        // 获取参数填写会话详情
        $sessionResult = ParameterSessionLogic::getSessionDetail($session_id, $this->user_id);
        if (!$sessionResult['success']) {
            return $this->_error($sessionResult['error']);
        }
        
        $sessionData = $sessionResult['data'];
        
        // 检查会话是否完成
        if ($sessionData['completion_rate'] < 100) {
            return $this->_error('参数填写未完成，无法创建订单');
        }
        
        // 获取收货地址
        $address = Db::name('user_address')
            ->where(['id' => $address_id, 'user_id' => $this->user_id, 'del' => 0])
            ->find();
        
        if (!$address) {
            return $this->_error('收货地址不存在');
        }
        
        Db::startTrans();
        try {
            // 创建订单数据
            $orderData = [
                'user_id' => $this->user_id,
                'order_sn' => $this->generateOrderSn(),
                'order_type' => 2, // 服务订单
                'goods_id' => $sessionData['goods_info']['id'],
                'goods_name' => $sessionData['goods_info']['name'],
                'goods_price' => $sessionData['goods_info']['price'],
                'total_amount' => $sessionData['goods_info']['price'],
                'consignee' => $address['consignee'],
                'mobile' => $address['mobile'],
                'province' => $address['province'],
                'city' => $address['city'],
                'district' => $address['district'],
                'address' => $address['address'],
                'remark' => $remark,
                'order_status' => 0, // 待支付
                'pay_status' => 0, // 未支付
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $order_id = Db::name('order')->insertGetId($orderData);
            
            // 创建订单参数记录
            $parameterRecord = [
                'order_id' => $order_id,
                'session_id' => $session_id,
                'template_config_id' => $sessionData['template_config']['id'],
                'parameter_values' => json_encode($sessionData['parameter_values']),
                'print_options' => json_encode($print_options),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('template_order_params')->insert($parameterRecord);
            
            // 创建服务交付记录
            $deliveryRecord = [
                'order_id' => $order_id,
                'user_id' => $this->user_id,
                'goods_id' => $sessionData['goods_info']['id'],
                'template_config_id' => $sessionData['template_config']['id'],
                'parameter_values' => json_encode($sessionData['parameter_values']),
                'print_options' => json_encode($print_options),
                'status' => 'pending_payment',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            Db::name('service_delivery_records')->insert($deliveryRecord);
            
            // 标记会话为已完成
            Db::name('parameter_fill_sessions')
                ->where('id', $session_id)
                ->update([
                    'is_completed' => 1,
                    'completed_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            Db::commit();
            
            // 生成支付信息
            $payment_info = $this->generatePaymentInfo($order_id, $orderData['total_amount'], $payment_method);
            
            return $this->_success('订单创建成功', [
                'order_id' => $order_id,
                'order_sn' => $orderData['order_sn'],
                'total_amount' => $orderData['total_amount'],
                'goods_info' => [
                    'id' => $sessionData['goods_info']['id'],
                    'name' => $sessionData['goods_info']['name'],
                    'image' => $sessionData['goods_info']['image'] ?? ''
                ],
                'delivery_info' => [
                    'consignee' => $address['consignee'],
                    'mobile' => $address['mobile'],
                    'address' => $address['province'] . $address['city'] . $address['district'] . $address['address'],
                    'estimated_delivery_time' => '3-5个工作日'
                ],
                'payment_info' => $payment_info,
                'service_info' => [
                    'template_name' => $sessionData['template_config']['name'],
                    'parameter_values' => $sessionData['parameter_values'],
                    'print_options' => $print_options
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->_error('订单创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取服务订单状态
     */
    public function status()
    {
        $order_id = $this->request->param('order_id');
        
        if (empty($order_id)) {
            return $this->_error('订单ID不能为空');
        }
        
        // 获取订单信息
        $order = Db::name('order')
            ->where(['id' => $order_id, 'user_id' => $this->user_id])
            ->find();
        
        if (!$order) {
            return $this->_error('订单不存在');
        }
        
        // 获取服务交付记录
        $deliveryRecord = Db::name('service_delivery_records')
            ->where('order_id', $order_id)
            ->find();
        
        // 构建进度步骤
        $steps = [
            ['step' => 1, 'name' => '订单确认', 'status' => 'completed', 'time' => date('Y-m-d H:i:s', $order['create_time'])],
            ['step' => 2, 'name' => '支付完成', 'status' => $order['pay_status'] == 1 ? 'completed' : 'pending', 'time' => $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : null],
            ['step' => 3, 'name' => '海报生成', 'status' => 'pending', 'time' => null],
            ['step' => 4, 'name' => '打印制作', 'status' => 'pending', 'time' => null],
            ['step' => 5, 'name' => '物流配送', 'status' => 'pending', 'time' => null]
        ];
        
        $current_step = 1;
        if ($order['pay_status'] == 1) {
            $current_step = 2;
            if ($deliveryRecord) {
                switch ($deliveryRecord['status']) {
                    case 'generating':
                    case 'generated':
                        $current_step = 3;
                        $steps[2]['status'] = 'processing';
                        if ($deliveryRecord['status'] == 'generated') {
                            $steps[2]['status'] = 'completed';
                            $steps[2]['time'] = $deliveryRecord['generated_at'];
                        }
                        break;
                    case 'printing':
                    case 'printed':
                        $current_step = 4;
                        $steps[2]['status'] = 'completed';
                        $steps[3]['status'] = 'processing';
                        if ($deliveryRecord['status'] == 'printed') {
                            $steps[3]['status'] = 'completed';
                            $steps[3]['time'] = $deliveryRecord['printed_at'];
                        }
                        break;
                    case 'shipped':
                    case 'delivered':
                        $current_step = 5;
                        $steps[2]['status'] = 'completed';
                        $steps[3]['status'] = 'completed';
                        $steps[4]['status'] = $deliveryRecord['status'] == 'delivered' ? 'completed' : 'processing';
                        if ($deliveryRecord['shipped_at']) {
                            $steps[4]['time'] = $deliveryRecord['shipped_at'];
                        }
                        break;
                }
            }
        }
        
        return $this->_success('获取成功', [
            'order_id' => $order['id'],
            'order_sn' => $order['order_sn'],
            'order_status' => $this->getOrderStatusText($order['order_status']),
            'order_status_text' => $this->getOrderStatusText($order['order_status']),
            'pay_status' => $order['pay_status'] == 1 ? 'paid' : 'unpaid',
            'pay_status_text' => $order['pay_status'] == 1 ? '已支付' : '未支付',
            'delivery_status' => $deliveryRecord['status'] ?? 'pending_payment',
            'delivery_status_text' => $this->getDeliveryStatusText($deliveryRecord['status'] ?? 'pending_payment'),
            'created_at' => date('Y-m-d H:i:s', $order['create_time']),
            'paid_at' => $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : null,
            'estimated_delivery_time' => date('Y-m-d H:i:s', $order['create_time'] + 5*24*3600), // 5天后
            'progress' => [
                'current_step' => $current_step,
                'total_steps' => 5,
                'steps' => $steps
            ]
        ]);
    }

    /**
     * 获取交付信息
     */
    public function delivery()
    {
        $order_id = $this->request->param('order_id');
        
        if (empty($order_id)) {
            return $this->_error('订单ID不能为空');
        }
        
        // 验证订单所有权
        $order = Db::name('order')
            ->where(['id' => $order_id, 'user_id' => $this->user_id])
            ->find();
        
        if (!$order) {
            return $this->_error('订单不存在');
        }
        
        // 获取服务交付记录
        $deliveryRecord = Db::name('service_delivery_records')
            ->where('order_id', $order_id)
            ->find();
        
        if (!$deliveryRecord) {
            return $this->_error('交付记录不存在');
        }
        
        // 获取订单参数
        $orderParams = Db::name('template_order_params')
            ->where('order_id', $order_id)
            ->find();
        
        $print_options = $orderParams ? json_decode($orderParams['print_options'], true) : [];
        
        // 构建时间线
        $timeline = [
            ['time' => date('Y-m-d H:i:s', $order['create_time']), 'status' => '订单创建', 'description' => '订单创建成功']
        ];
        
        if ($order['pay_time']) {
            $timeline[] = ['time' => date('Y-m-d H:i:s', $order['pay_time']), 'status' => '支付完成', 'description' => '支付成功'];
        }
        
        if ($deliveryRecord['generated_at']) {
            $timeline[] = ['time' => $deliveryRecord['generated_at'], 'status' => '海报生成', 'description' => '海报生成完成'];
        }
        
        if ($deliveryRecord['printed_at']) {
            $timeline[] = ['time' => $deliveryRecord['printed_at'], 'status' => '打印完成', 'description' => '海报打印完成'];
        }
        
        if ($deliveryRecord['shipped_at']) {
            $timeline[] = ['time' => $deliveryRecord['shipped_at'], 'status' => '已发货', 'description' => '快递已揽收'];
        }
        
        return $this->_success('获取成功', [
            'order_id' => $order['id'],
            'delivery_status' => $deliveryRecord['status'],
            'delivery_status_text' => $this->getDeliveryStatusText($deliveryRecord['status']),
            'poster_info' => [
                'preview_url' => $deliveryRecord['poster_preview_url'] ?? '',
                'download_url' => $deliveryRecord['poster_download_url'] ?? '',
                'generated_at' => $deliveryRecord['generated_at'],
                'file_size' => $deliveryRecord['file_size'] ?? '',
                'resolution' => '300DPI'
            ],
            'print_info' => [
                'material' => $print_options['material'] ?? 'premium_paper',
                'size' => $print_options['size'] ?? 'A4',
                'quantity' => $print_options['quantity'] ?? 1,
                'print_quality' => $deliveryRecord['print_quality'] ?? 'excellent',
                'printed_at' => $deliveryRecord['printed_at']
            ],
            'shipping_info' => [
                'express_company' => $deliveryRecord['express_company'] ?? '',
                'tracking_number' => $deliveryRecord['tracking_number'] ?? '',
                'shipped_at' => $deliveryRecord['shipped_at'],
                'estimated_arrival' => $deliveryRecord['estimated_arrival'],
                'current_location' => $deliveryRecord['current_location'] ?? '',
                'delivery_address' => $order['province'] . $order['city'] . $order['district'] . $order['address']
            ],
            'timeline' => $timeline
        ]);
    }

    /**
     * 下载生成文件
     */
    public function download()
    {
        $order_id = $this->request->param('order_id');
        $file_type = $this->request->post('file_type', 'pdf');
        $quality = $this->request->post('quality', 'high');
        
        if (empty($order_id)) {
            return $this->_error('订单ID不能为空');
        }
        
        // 验证订单所有权
        $order = Db::name('order')
            ->where(['id' => $order_id, 'user_id' => $this->user_id])
            ->find();
        
        if (!$order) {
            return $this->_error('订单不存在');
        }
        
        // 获取服务交付记录
        $deliveryRecord = Db::name('service_delivery_records')
            ->where('order_id', $order_id)
            ->find();
        
        if (!$deliveryRecord || empty($deliveryRecord['poster_download_url'])) {
            return $this->_error('文件尚未生成，请稍后再试');
        }
        
        // 生成下载链接（带时效性）
        $download_token = md5($order_id . time() . $this->user_id);
        $expires_at = date('Y-m-d H:i:s', time() + 2*3600); // 2小时后过期
        
        // 这里可以根据需要生成不同质量的文件链接
        $file_suffix = $quality === 'high' ? '_high' : '';
        $download_url = $deliveryRecord['poster_download_url'];
        
        return $this->_success('下载链接生成成功', [
            'download_url' => $download_url,
            'file_name' => $order['goods_name'] . '_' . ($quality === 'high' ? '高清版' : '标准版') . '.' . $file_type,
            'file_size' => $deliveryRecord['file_size'] ?? '2.5MB',
            'expires_at' => $expires_at,
            'download_token' => $download_token
        ]);
    }

    /**
     * 获取用户服务订单列表
     */
    public function lists()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $status = $this->request->get('status', 'all');
        
        $where = ['user_id' => $this->user_id, 'order_type' => 2]; // 服务订单
        
        // 状态筛选
        if ($status !== 'all') {
            switch ($status) {
                case 'unpaid':
                    $where['pay_status'] = 0;
                    break;
                case 'paid':
                    $where['pay_status'] = 1;
                    break;
                case 'completed':
                    $where['order_status'] = 3; // 已完成
                    break;
                case 'cancelled':
                    $where['order_status'] = 4; // 已取消
                    break;
            }
        }
        
        $orders = Db::name('order')
            ->alias('o')
            ->leftJoin('service_delivery_records sdr', 'o.id = sdr.order_id')
            ->where($where)
            ->field('o.*, sdr.status as delivery_status, sdr.poster_download_url')
            ->order('o.create_time desc')
            ->paginate($limit, false, ['page' => $page]);
        
        $list = $orders->items();
        
        // 处理数据
        foreach ($list as &$item) {
            $item['order_status_text'] = $this->getOrderStatusText($item['order_status']);
            $item['delivery_status_text'] = $this->getDeliveryStatusText($item['delivery_status'] ?? 'pending_payment');
            $item['created_at'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['estimated_delivery_time'] = date('Y-m-d H:i:s', $item['create_time'] + 5*24*3600);
            $item['can_download'] = !empty($item['poster_download_url']);
            $item['can_cancel'] = $item['order_status'] == 0 && $item['pay_status'] == 0; // 未支付可取消
            
            // 移除敏感字段
            unset($item['create_time'], $item['update_time'], $item['pay_time']);
        }
        
        return $this->_success('获取成功', [
            'list' => $list,
            'total' => $orders->total(),
            'page' => $page,
            'limit' => $limit,
            'has_more' => $page * $limit < $orders->total()
        ]);
    }

    /**
     * 取消服务订单
     */
    public function cancel()
    {
        $order_id = $this->request->param('order_id');
        $cancel_reason = $this->request->post('cancel_reason', '');
        
        if (empty($order_id)) {
            return $this->_error('订单ID不能为空');
        }
        
        // 验证订单所有权和状态
        $order = Db::name('order')
            ->where(['id' => $order_id, 'user_id' => $this->user_id])
            ->find();
        
        if (!$order) {
            return $this->_error('订单不存在');
        }
        
        if ($order['order_status'] != 0 || $order['pay_status'] != 0) {
            return $this->_error('订单状态不允许取消');
        }
        
        Db::startTrans();
        try {
            // 更新订单状态
            Db::name('order')
                ->where('id', $order_id)
                ->update([
                    'order_status' => 4, // 已取消
                    'cancel_time' => time(),
                    'cancel_reason' => $cancel_reason,
                    'update_time' => time()
                ]);
            
            // 更新服务交付记录状态
            Db::name('service_delivery_records')
                ->where('order_id', $order_id)
                ->update([
                    'status' => 'cancelled',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            Db::commit();
            
            return $this->_success('订单取消成功', [
                'order_id' => $order_id,
                'cancel_time' => date('Y-m-d H:i:s'),
                'refund_info' => [
                    'refund_amount' => $order['total_amount'],
                    'refund_method' => '原路退回',
                    'estimated_refund_time' => '1-3个工作日'
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return $this->_error('取消失败：' . $e->getMessage());
        }
    }

    /**
     * 生成订单号
     */
    private function generateOrderSn()
    {
        return 'SO' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 生成支付信息
     */
    private function generatePaymentInfo($order_id, $amount, $payment_method)
    {
        // 这里应该调用实际的支付服务
        // 暂时返回模拟数据
        return [
            'payment_method' => $payment_method,
            'payment_url' => 'weixin://wxpay/bizpayurl?pr=' . base64_encode($order_id),
            'expires_at' => date('Y-m-d H:i:s', time() + 30*60) // 30分钟后过期
        ];
    }

    /**
     * 获取订单状态文本
     */
    private function getOrderStatusText($status)
    {
        $statusMap = [
            0 => '待支付',
            1 => '待发货',
            2 => '已发货',
            3 => '已完成',
            4 => '已取消'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取交付状态文本
     */
    private function getDeliveryStatusText($status)
    {
        $statusMap = [
            'pending_payment' => '待支付',
            'generating' => '生成中',
            'generated' => '已生成',
            'printing' => '打印中',
            'printed' => '已打印',
            'shipped' => '已发货',
            'delivered' => '已送达',
            'cancelled' => '已取消'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
}
