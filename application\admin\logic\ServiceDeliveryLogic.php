<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\logic;

use think\Db;
use think\Exception;
use app\common\server\PosterTemplateService;

class ServiceDeliveryLogic
{
    public static $error = '';

    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * 获取服务交付列表
     * @param array $params
     * @return array
     */
    public static function lists($params = [])
    {
        try {
            $where = [];
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            // 搜索条件
            if (!empty($params['order_sn'])) {
                $where[] = ['o.order_sn', 'like', '%' . $params['order_sn'] . '%'];
            }

            if (!empty($params['delivery_status'])) {
                $where[] = ['sdr.delivery_status', '=', $params['delivery_status']];
            }

            if (!empty($params['date_range'])) {
                $dateRange = explode(' - ', $params['date_range']);
                if (count($dateRange) == 2) {
                    $where[] = ['sdr.created_at', 'between', [strtotime($dateRange[0]), strtotime($dateRange[1])]];
                }
            }

            // 查询数据
            $query = Db::name('service_delivery_records')
                ->alias('sdr')
                ->leftJoin('order o', 'sdr.order_id = o.id')
                ->leftJoin('user u', 'o.user_id = u.id')
                ->leftJoin('goods g', 'o.goods_id = g.id')
                ->where($where)
                ->field([
                    'sdr.*',
                    'o.order_sn',
                    'o.order_amount',
                    'u.nickname as user_nickname',
                    'g.name as goods_name'
                ])
                ->order('sdr.created_at desc');

            // 分页查询
            $total = $query->count();
            $lists = $query->page($page, $limit)->select();

            return [
                'count' => $total,
                'data' => $lists,
                'page' => $page,
                'limit' => $limit
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'count' => 0,
                'data' => [],
                'page' => 1,
                'limit' => 20
            ];
        }
    }

    /**
     * 获取服务交付详情
     * @param string $id
     * @return array|false
     */
    public static function detail($id)
    {
        try {
            $detail = Db::name('service_delivery_records')
                ->alias('sdr')
                ->leftJoin('order o', 'sdr.order_id = o.id')
                ->leftJoin('user u', 'o.user_id = u.id')
                ->leftJoin('goods g', 'o.goods_id = g.id')
                ->leftJoin('template_order_params top', 'sdr.order_id = top.order_id')
                ->where('sdr.id', $id)
                ->field([
                    'sdr.*',
                    'o.order_sn',
                    'o.order_amount',
                    'o.order_status',
                    'u.nickname as user_nickname',
                    'u.mobile as user_mobile',
                    'g.name as goods_name',
                    'g.image as goods_image',
                    'top.parameter_values'
                ])
                ->find();

            if (!$detail) {
                self::$error = '服务交付记录不存在';
                return false;
            }

            // 解析参数值
            if (!empty($detail['parameter_values'])) {
                $detail['parameter_values_parsed'] = json_decode($detail['parameter_values'], true);
            } else {
                $detail['parameter_values_parsed'] = [];
            }

            return $detail;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 编辑服务交付记录
     * @param array $data
     * @return bool
     */
    public static function edit($data)
    {
        try {
            if (empty($data['id'])) {
                self::$error = '记录ID不能为空';
                return false;
            }

            // 检查记录是否存在
            $exists = Db::name('service_delivery_records')->where('id', $data['id'])->find();
            if (!$exists) {
                self::$error = '服务交付记录不存在';
                return false;
            }

            $updateData = [];

            // 更新交付状态
            if (isset($data['delivery_status'])) {
                $updateData['delivery_status'] = $data['delivery_status'];
            }

            // 更新物流信息
            if (isset($data['shipping_company'])) {
                $updateData['shipping_company'] = $data['shipping_company'];
            }
            if (isset($data['shipping_number'])) {
                $updateData['shipping_number'] = $data['shipping_number'];
            }

            // 更新错误信息
            if (isset($data['error_message'])) {
                $updateData['error_message'] = $data['error_message'];
            }

            if (empty($updateData)) {
                self::$error = '没有需要更新的数据';
                return false;
            }

            $updateData['updated_at'] = time();

            $result = Db::name('service_delivery_records')
                ->where('id', $data['id'])
                ->update($updateData);

            return $result !== false;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 删除服务交付记录
     * @param string|array $ids
     * @return bool
     */
    public static function delete($ids)
    {
        try {
            if (empty($ids)) {
                self::$error = '记录ID不能为空';
                return false;
            }

            if (is_array($ids)) {
                $where = ['id', 'in', $ids];
            } else {
                $where = ['id', '=', $ids];
            }

            $result = Db::name('service_delivery_records')->where($where)->delete();

            return $result !== false;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 更新交付状态
     * @param array $data
     * @return bool
     */
    public static function updateStatus($data)
    {
        try {
            if (empty($data['id']) || empty($data['status'])) {
                self::$error = '参数不完整';
                return false;
            }

            $updateData = [
                'delivery_status' => $data['status'],
                'updated_at' => time()
            ];

            // 如果是完成状态，记录交付时间
            if ($data['status'] == 'completed') {
                $updateData['delivery_time'] = time();
            }

            $result = Db::name('service_delivery_records')
                ->where('id', $data['id'])
                ->update($updateData);

            return $result !== false;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 更新物流信息
     * @param array $data
     * @return bool
     */
    public static function updateShipping($data)
    {
        try {
            if (empty($data['id'])) {
                self::$error = '记录ID不能为空';
                return false;
            }

            $updateData = [
                'updated_at' => time()
            ];

            if (isset($data['shipping_company'])) {
                $updateData['shipping_company'] = $data['shipping_company'];
            }
            if (isset($data['shipping_number'])) {
                $updateData['shipping_number'] = $data['shipping_number'];
            }

            // 如果有物流信息，更新状态为已发货
            if (!empty($data['shipping_number'])) {
                $updateData['delivery_status'] = 'shipped';
            }

            $result = Db::name('service_delivery_records')
                ->where('id', $data['id'])
                ->update($updateData);

            return $result !== false;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 导出交付数据
     * @param array $params
     * @return array|false
     */
    public static function export($params = [])
    {
        try {
            $where = [];

            // 搜索条件
            if (!empty($params['order_sn'])) {
                $where[] = ['o.order_sn', 'like', '%' . $params['order_sn'] . '%'];
            }

            if (!empty($params['delivery_status'])) {
                $where[] = ['sdr.delivery_status', '=', $params['delivery_status']];
            }

            if (!empty($params['ids'])) {
                $ids = is_array($params['ids']) ? $params['ids'] : explode(',', $params['ids']);
                $where[] = ['sdr.id', 'in', $ids];
            }

            // 查询数据
            $lists = Db::name('service_delivery_records')
                ->alias('sdr')
                ->leftJoin('order o', 'sdr.order_id = o.id')
                ->leftJoin('user u', 'o.user_id = u.id')
                ->leftJoin('goods g', 'o.goods_id = g.id')
                ->where($where)
                ->field([
                    'sdr.id',
                    'o.order_sn',
                    'u.nickname as user_nickname',
                    'g.name as goods_name',
                    'sdr.delivery_status',
                    'sdr.shipping_company',
                    'sdr.shipping_number',
                    'sdr.delivery_time',
                    'sdr.created_at'
                ])
                ->order('sdr.created_at desc')
                ->select();

            // 处理数据
            $exportData = [];
            foreach ($lists as $item) {
                $statusMap = [
                    'pending' => '待处理',
                    'generating' => '生成中',
                    'generated' => '已生成',
                    'printing' => '打印中',
                    'printed' => '已打印',
                    'shipping' => '配送中',
                    'shipped' => '已发货',
                    'completed' => '已完成',
                    'failed' => '失败'
                ];

                $exportData[] = [
                    'ID' => $item['id'],
                    '订单编号' => $item['order_sn'],
                    '用户昵称' => $item['user_nickname'],
                    '商品名称' => $item['goods_name'],
                    '交付状态' => $statusMap[$item['delivery_status']] ?? $item['delivery_status'],
                    '物流公司' => $item['shipping_company'] ?: '-',
                    '物流单号' => $item['shipping_number'] ?: '-',
                    '交付时间' => $item['delivery_time'] ? date('Y-m-d H:i:s', $item['delivery_time']) : '-',
                    '创建时间' => date('Y-m-d H:i:s', $item['created_at'])
                ];
            }

            return $exportData;

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取生成任务列表
     * @param array $params 查询参数
     * @return array
     */
    public static function getGenerationTasks($params = [])
    {
        try {
            $where = [];
            $limit = $params['limit'] ?? 20;
            $page = $params['page'] ?? 1;

            // 只获取待生成(1)和生成中(2)的任务
            $where[] = ['sdr.delivery_status', 'in', [1, 2]];

            // 时间范围筛选
            if (!empty($params['start_date'])) {
                $where[] = ['sdr.created_at', '>=', strtotime($params['start_date'])];
            }
            if (!empty($params['end_date'])) {
                $where[] = ['sdr.created_at', '<=', strtotime($params['end_date'] . ' 23:59:59')];
            }

            // 查询任务列表
            $lists = Db::name('service_delivery_records')
                ->alias('sdr')
                ->join('order o', 'o.id = sdr.order_id')
                ->join('order_goods og', 'og.id = sdr.order_goods_id')
                ->join('goods g', 'g.id = og.goods_id')
                ->join('user u', 'u.id = o.user_id')
                ->where($where)
                ->field('sdr.*, o.order_sn, o.user_id, og.goods_name, og.goods_image, og.order_params, u.nickname, u.mobile')
                ->order('sdr.created_at desc')
                ->page($page, $limit)
                ->select();

            // 格式化数据
            foreach ($lists as &$item) {
                $item['order_params'] = json_decode($item['order_params'], true);
                $item['generated_files'] = $item['generated_files'] ? json_decode($item['generated_files'], true) : null;
                $item['status_text'] = self::getStatusText($item['delivery_status']);
                $item['created_at'] = date('Y-m-d H:i:s', $item['created_at']);
                $item['updated_at'] = date('Y-m-d H:i:s', $item['updated_at']);
            }

            // 获取总数
            $count = Db::name('service_delivery_records')
                ->alias('sdr')
                ->join('order o', 'o.id = sdr.order_id')
                ->where($where)
                ->count();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'lists' => $lists,
                    'count' => $count,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 执行海报生成任务
     * @param string $recordId 交付记录ID
     * @return array
     */
    public static function executeGenerationTask($recordId)
    {
        Db::startTrans();
        try {
            // 获取交付记录
            $record = Db::name('service_delivery_records')->where('id', $recordId)->find();
            if (!$record) {
                throw new Exception('交付记录不存在');
            }

            if (!in_array($record['delivery_status'], [1, 2])) {
                throw new Exception('当前状态不允许执行生成任务');
            }

            // 更新状态为生成中
            Db::name('service_delivery_records')
                ->where('id', $recordId)
                ->update([
                    'delivery_status' => 2,
                    'updated_at' => time()
                ]);

            // 获取订单参数
            $orderGoods = Db::name('order_goods')
                ->where('id', $record['order_goods_id'])
                ->find();

            if (!$orderGoods) {
                throw new Exception('订单商品不存在');
            }

            $orderParams = json_decode($orderGoods['order_params'], true);
            if (empty($orderParams)) {
                throw new Exception('订单参数为空');
            }

            // 调用海报生成服务
            $posterService = new PosterTemplateService();
            $generationResult = $posterService->generatePoster(
                $orderParams['template_config_id'],
                $orderParams['parameter_values']
            );

            if ($generationResult['success']) {
                // 生成成功，更新状态为待打印
                Db::name('service_delivery_records')
                    ->where('id', $recordId)
                    ->update([
                        'delivery_status' => 3,
                        'generated_files' => json_encode($generationResult['files']),
                        'generation_time' => $generationResult['generation_time'] ?? 0,
                        'updated_at' => time()
                    ]);

                Db::commit();
                return [
                    'code' => 1,
                    'msg' => '海报生成成功',
                    'data' => [
                        'record_id' => $recordId,
                        'files' => $generationResult['files'],
                        'generation_time' => $generationResult['generation_time'] ?? 0
                    ]
                ];
            } else {
                // 生成失败，处理重试逻辑
                $newRetryCount = $record['retry_count'] + 1;
                $maxRetryCount = $record['max_retry_count'] ?? 3;

                if ($newRetryCount < $maxRetryCount) {
                    // 还可以重试，状态回到待生成
                    Db::name('service_delivery_records')
                        ->where('id', $recordId)
                        ->update([
                            'delivery_status' => 1,
                            'retry_count' => $newRetryCount,
                            'error_message' => $generationResult['error'],
                            'updated_at' => time()
                        ]);
                } else {
                    // 超过最大重试次数，标记为失败
                    Db::name('service_delivery_records')
                        ->where('id', $recordId)
                        ->update([
                            'delivery_status' => 8,
                            'retry_count' => $newRetryCount,
                            'error_message' => $generationResult['error'],
                            'updated_at' => time()
                        ]);
                }

                Db::commit();
                return [
                    'code' => 0,
                    'msg' => '海报生成失败：' . $generationResult['error'],
                    'data' => [
                        'record_id' => $recordId,
                        'retry_count' => $newRetryCount,
                        'max_retry_count' => $maxRetryCount,
                        'can_retry' => $newRetryCount < $maxRetryCount
                    ]
                ];
            }

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 批量生成海报任务
     * @param array $recordIds 记录ID数组
     * @return array
     */
    public static function batchGenerate($recordIds)
    {
        $results = [
            'total' => count($recordIds),
            'success' => 0,
            'failed' => 0,
            'details' => []
        ];

        foreach ($recordIds as $recordId) {
            $result = self::executeGenerationTask($recordId);
            if ($result['code'] == 1) {
                $results['success']++;
                $results['details'][$recordId] = [
                    'status' => 'success',
                    'message' => '生成成功'
                ];
            } else {
                $results['failed']++;
                $results['details'][$recordId] = [
                    'status' => 'failed',
                    'message' => $result['msg']
                ];
            }
        }

        return [
            'code' => 1,
            'msg' => "批量处理完成，成功{$results['success']}个，失败{$results['failed']}个",
            'data' => $results
        ];
    }

    /**
     * 获取打印任务列表
     * @param array $params 查询参数
     * @return array
     */
    public static function getPrintTasks($params = [])
    {
        try {
            $where = [];
            $limit = $params['limit'] ?? 20;
            $page = $params['page'] ?? 1;

            // 只获取待打印(3)和打印中(4)的任务
            $where[] = ['sdr.delivery_status', 'in', [3, 4]];

            // 优先级排序：打印中的任务优先显示
            $order = 'sdr.delivery_status desc, sdr.created_at asc';

            // 查询任务列表
            $lists = Db::name('service_delivery_records')
                ->alias('sdr')
                ->join('order o', 'o.id = sdr.order_id')
                ->join('order_goods og', 'og.id = sdr.order_goods_id')
                ->join('goods g', 'g.id = og.goods_id')
                ->join('user u', 'u.id = o.user_id')
                ->where($where)
                ->field('sdr.*, o.order_sn, o.user_id, og.goods_name, og.goods_image, u.nickname, u.mobile')
                ->order($order)
                ->page($page, $limit)
                ->select();

            // 格式化数据
            foreach ($lists as &$item) {
                $item['generated_files'] = json_decode($item['generated_files'], true);
                $item['print_info'] = $item['print_info'] ? json_decode($item['print_info'], true) : null;
                $item['status_text'] = self::getStatusText($item['delivery_status']);
                $item['created_at'] = date('Y-m-d H:i:s', $item['created_at']);
                $item['updated_at'] = date('Y-m-d H:i:s', $item['updated_at']);
                $item['print_time'] = $item['print_time'] ? date('Y-m-d H:i:s', $item['print_time']) : null;
            }

            // 获取总数
            $count = Db::name('service_delivery_records')
                ->alias('sdr')
                ->where($where)
                ->count();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'lists' => $lists,
                    'count' => $count,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 更新打印状态
     * @param string $recordId 记录ID
     * @param int $printStatus 打印状态
     * @param array $printInfo 打印信息
     * @return array
     */
    public static function updatePrintStatus($recordId, $printStatus, $printInfo = [])
    {
        try {
            $record = Db::name('service_delivery_records')->where('id', $recordId)->find();
            if (!$record) {
                throw new Exception('交付记录不存在');
            }

            if (!in_array($record['delivery_status'], [3, 4])) {
                throw new Exception('当前状态不允许更新打印状态');
            }

            $updateData = [
                'delivery_status' => $printStatus,
                'updated_at' => time()
            ];

            if (!empty($printInfo)) {
                $updateData['print_info'] = json_encode($printInfo);
            }

            if ($printStatus == 4) {
                // 开始打印
                $updateData['print_time'] = time();
            } elseif ($printStatus == 5) {
                // 打印完成，状态更新为待发货
                $updateData['print_completed_at'] = time();
            }

            Db::name('service_delivery_records')
                ->where('id', $recordId)
                ->update($updateData);

            return [
                'code' => 1,
                'msg' => '打印状态更新成功',
                'data' => [
                    'record_id' => $recordId,
                    'status' => $printStatus,
                    'status_text' => self::getStatusText($printStatus)
                ]
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取状态文本
     * @param int $status 状态码
     * @return string
     */
    private static function getStatusText($status)
    {
        $statusMap = [
            1 => '待生成',
            2 => '生成中',
            3 => '待打印',
            4 => '打印中',
            5 => '待发货',
            6 => '已发货',
            7 => '已完成',
            8 => '失败'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取物流管理列表
     * @param array $params 查询参数
     * @return array
     */
    public static function getLogisticsTasks($params = [])
    {
        try {
            $where = [];
            $limit = $params['limit'] ?? 20;
            $page = $params['page'] ?? 1;

            // 根据状态筛选
            if (isset($params['status']) && $params['status'] !== 'all') {
                if ($params['status'] == 'pending') {
                    $where[] = ['sdr.delivery_status', '=', 5]; // 待发货
                } elseif ($params['status'] == 'shipped') {
                    $where[] = ['sdr.delivery_status', '=', 6]; // 已发货
                }
            } else {
                // 默认显示待发货和已发货的任务
                $where[] = ['sdr.delivery_status', 'in', [5, 6]];
            }

            // 时间范围筛选
            if (!empty($params['start_date'])) {
                $where[] = ['sdr.created_at', '>=', strtotime($params['start_date'])];
            }
            if (!empty($params['end_date'])) {
                $where[] = ['sdr.created_at', '<=', strtotime($params['end_date'] . ' 23:59:59')];
            }

            // 查询任务列表
            $lists = Db::name('service_delivery_records')
                ->alias('sdr')
                ->join('order o', 'o.id = sdr.order_id')
                ->join('order_goods og', 'og.id = sdr.order_goods_id')
                ->join('goods g', 'g.id = og.goods_id')
                ->join('user u', 'u.id = o.user_id')
                ->where($where)
                ->field('sdr.*, o.order_sn, o.user_id, o.consignee, o.mobile as consignee_mobile, o.address, og.goods_name, og.goods_image, u.nickname')
                ->order('sdr.delivery_status asc, sdr.updated_at desc')
                ->page($page, $limit)
                ->select();

            // 格式化数据
            foreach ($lists as &$item) {
                $item['print_info'] = $item['print_info'] ? json_decode($item['print_info'], true) : null;
                $item['logistics_info'] = $item['logistics_info'] ? json_decode($item['logistics_info'], true) : null;
                $item['status_text'] = self::getStatusText($item['delivery_status']);
                $item['created_at'] = date('Y-m-d H:i:s', $item['created_at']);
                $item['updated_at'] = date('Y-m-d H:i:s', $item['updated_at']);
                $item['ship_time'] = $item['ship_time'] ? date('Y-m-d H:i:s', $item['ship_time']) : null;

                // 格式化收货地址
                $item['full_address'] = $item['address'];
                $item['consignee_info'] = [
                    'name' => $item['consignee'],
                    'mobile' => $item['consignee_mobile'],
                    'address' => $item['address']
                ];
            }

            // 获取总数
            $count = Db::name('service_delivery_records')
                ->alias('sdr')
                ->where($where)
                ->count();

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'lists' => $lists,
                    'count' => $count,
                    'page' => $page,
                    'limit' => $limit
                ]
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 批量录入物流信息
     * @param array $logisticsData 物流数据数组
     * @return array
     */
    public static function batchInputLogistics($logisticsData)
    {
        Db::startTrans();
        try {
            $results = [
                'total' => count($logisticsData),
                'success' => 0,
                'failed' => 0,
                'updated_records' => []
            ];

            foreach ($logisticsData as $data) {
                try {
                    $recordId = $data['record_id'];

                    // 检查记录是否存在且状态正确
                    $record = Db::name('service_delivery_records')->where('id', $recordId)->find();
                    if (!$record || $record['delivery_status'] != 5) {
                        throw new Exception("记录{$recordId}不存在或状态不正确");
                    }

                    // 更新物流信息
                    $logisticsInfo = [
                        'company' => $data['logistics_company'],
                        'tracking_number' => $data['tracking_number'],
                        'ship_time' => $data['ship_time']
                    ];

                    Db::name('service_delivery_records')
                        ->where('id', $recordId)
                        ->update([
                            'delivery_status' => 6, // 已发货
                            'tracking_number' => $data['tracking_number'],
                            'logistics_info' => json_encode($logisticsInfo),
                            'ship_time' => $data['ship_time'],
                            'updated_at' => time()
                        ]);

                    $results['success']++;
                    $results['updated_records'][] = $recordId;

                } catch (Exception $e) {
                    $results['failed']++;
                    // 记录具体的失败原因，但不中断整个批量操作
                }
            }

            Db::commit();
            return [
                'code' => 1,
                'msg' => "批量录入完成，成功{$results['success']}个，失败{$results['failed']}个",
                'data' => $results
            ];

        } catch (Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取交付统计信息
     * @param array $params 统计参数
     * @return array
     */
    public static function getDeliveryStatistics($params = [])
    {
        try {
            $where = [];

            // 时间范围筛选
            if (!empty($params['start_date'])) {
                $where[] = ['created_at', '>=', strtotime($params['start_date'])];
            }
            if (!empty($params['end_date'])) {
                $where[] = ['created_at', '<=', strtotime($params['end_date'] . ' 23:59:59')];
            }

            // 获取各状态的统计数据
            $statusStats = Db::name('service_delivery_records')
                ->where($where)
                ->field('delivery_status, count(*) as count')
                ->group('delivery_status')
                ->select();

            $stats = [
                'total_records' => 0,
                'pending_generation' => 0,
                'generating' => 0,
                'pending_print' => 0,
                'printing' => 0,
                'pending_ship' => 0,
                'shipped' => 0,
                'completed' => 0,
                'failed' => 0
            ];

            $statusMapping = [
                1 => 'pending_generation',
                2 => 'generating',
                3 => 'pending_print',
                4 => 'printing',
                5 => 'pending_ship',
                6 => 'shipped',
                7 => 'completed',
                8 => 'failed'
            ];

            foreach ($statusStats as $stat) {
                $stats['total_records'] += $stat['count'];
                if (isset($statusMapping[$stat['delivery_status']])) {
                    $stats[$statusMapping[$stat['delivery_status']]] = $stat['count'];
                }
            }

            // 计算成功率
            $successCount = $stats['completed'] + $stats['shipped'];
            $stats['success_rate'] = $stats['total_records'] > 0 ?
                round($successCount / $stats['total_records'] * 100, 2) : 0;

            // 计算平均处理时间
            $avgTimes = Db::name('service_delivery_records')
                ->where($where)
                ->where('delivery_status', 'in', [6, 7]) // 已发货或已完成
                ->field('AVG(generation_time) as avg_generation_time, AVG(updated_at - created_at) as avg_total_time')
                ->find();

            $stats['avg_generation_time'] = round($avgTimes['avg_generation_time'] ?? 0, 2);
            $stats['avg_total_time'] = round(($avgTimes['avg_total_time'] ?? 0) / 3600, 2); // 转换为小时

            return [
                'code' => 1,
                'msg' => '获取成功',
                'data' => $stats
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 处理异常交付记录
     * @return array
     */
    public static function processAbnormalRecords()
    {
        try {
            $currentTime = time();
            $results = [
                'total_abnormal' => 0,
                'reset_to_pending' => 0,
                'marked_as_failed' => 0,
                'notifications_sent' => 0
            ];

            // 查找异常记录
            // 1. 生成中状态超过2小时的记录
            $generatingTimeout = Db::name('service_delivery_records')
                ->where('delivery_status', 2)
                ->where('updated_at', '<', $currentTime - 2*3600)
                ->select();

            // 2. 打印中状态超过24小时的记录
            $printingTimeout = Db::name('service_delivery_records')
                ->where('delivery_status', 4)
                ->where('updated_at', '<', $currentTime - 24*3600)
                ->select();

            $abnormalRecords = array_merge($generatingTimeout, $printingTimeout);
            $results['total_abnormal'] = count($abnormalRecords);

            foreach ($abnormalRecords as $record) {
                $timeoutHours = ($currentTime - $record['updated_at']) / 3600;

                if ($record['delivery_status'] == 2) {
                    // 生成中超时处理
                    if ($timeoutHours < 24) {
                        // 超时不到24小时，重置为待生成
                        Db::name('service_delivery_records')
                            ->where('id', $record['id'])
                            ->update([
                                'delivery_status' => 1,
                                'error_message' => '生成超时，已重置为待生成',
                                'updated_at' => $currentTime
                            ]);
                        $results['reset_to_pending']++;
                    } else {
                        // 超时超过24小时，标记为失败
                        Db::name('service_delivery_records')
                            ->where('id', $record['id'])
                            ->update([
                                'delivery_status' => 8,
                                'error_message' => '生成严重超时，已标记为失败',
                                'updated_at' => $currentTime
                            ]);
                        $results['marked_as_failed']++;
                    }
                } elseif ($record['delivery_status'] == 4) {
                    // 打印中超时处理，直接标记为失败
                    Db::name('service_delivery_records')
                        ->where('id', $record['id'])
                        ->update([
                            'delivery_status' => 8,
                            'error_message' => '打印严重超时，已标记为失败',
                            'updated_at' => $currentTime
                        ]);
                    $results['marked_as_failed']++;
                }

                // 发送异常通知（这里可以集成消息通知系统）
                $results['notifications_sent']++;
            }

            return [
                'code' => 1,
                'msg' => '异常记录处理完成',
                'data' => $results
            ];

        } catch (Exception $e) {
            self::$error = $e->getMessage();
            return [
                'code' => 0,
                'msg' => $e->getMessage(),
                'data' => null
            ];
        }
    }
}
