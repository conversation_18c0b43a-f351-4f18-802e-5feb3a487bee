<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\common\middleware;

use app\common\server\PerformanceOptimizationService;
use think\Request;
use think\Response;
use think\Log;

/**
 * 性能监控中间件
 * 监控API接口的响应时间、错误率等性能指标
 */
class PerformanceMonitor
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next)
    {
        // 记录开始时间
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // 获取请求信息
        $method = $request->method();
        $uri = $request->url();
        $endpoint = $this->getEndpoint($uri);
        
        // 执行请求
        $response = $next($request);
        
        // 计算性能指标
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $responseTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
        $memoryUsage = $endMemory - $startMemory; // 字节
        $statusCode = $response->getCode();
        
        // 记录性能数据
        $this->recordPerformanceData([
            'method' => $method,
            'endpoint' => $endpoint,
            'uri' => $uri,
            'response_time' => $responseTime,
            'memory_usage' => $memoryUsage,
            'status_code' => $statusCode,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $request->header('User-Agent', ''),
            'ip' => $request->ip()
        ]);
        
        // 监控API性能
        PerformanceOptimizationService::monitorApiPerformance(
            $endpoint, 
            $method, 
            $responseTime, 
            $statusCode
        );
        
        // 添加性能头信息（开发环境）
        if (config('app_debug')) {
            $response->header([
                'X-Response-Time' => $responseTime . 'ms',
                'X-Memory-Usage' => $this->formatBytes($memoryUsage),
                'X-Peak-Memory' => $this->formatBytes(memory_get_peak_usage())
            ]);
        }
        
        return $response;
    }
    
    /**
     * 从URI中提取端点名称
     */
    private function getEndpoint($uri)
    {
        // 移除查询参数
        $path = parse_url($uri, PHP_URL_PATH);
        
        // 替换动态参数为占位符
        $endpoint = preg_replace('/\/\d+/', '/{id}', $path);
        $endpoint = preg_replace('/\/[a-f0-9]{32}/', '/{uuid}', $endpoint);
        $endpoint = preg_replace('/\/session_\w+/', '/session_{id}', $endpoint);
        
        return $endpoint;
    }
    
    /**
     * 记录性能数据
     */
    private function recordPerformanceData($data)
    {
        // 异步记录，避免影响响应时间
        try {
            // 记录到日志
            if ($data['response_time'] > 2000) {
                Log::warning('慢接口', $data);
            } elseif ($data['status_code'] >= 500) {
                Log::error('服务器错误', $data);
            } elseif ($data['status_code'] >= 400) {
                Log::notice('客户端错误', $data);
            }
            
            // 记录到性能统计
            $this->updatePerformanceStats($data);
            
        } catch (\Exception $e) {
            // 记录性能监控本身的错误，但不影响主流程
            Log::error('性能监控记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新性能统计
     */
    private function updatePerformanceStats($data)
    {
        $cacheKey = 'perf_stats_' . date('Y-m-d-H');
        
        // 获取当前小时的统计数据
        $stats = cache($cacheKey) ?: [
            'total_requests' => 0,
            'total_response_time' => 0,
            'total_memory_usage' => 0,
            'error_count' => 0,
            'slow_request_count' => 0,
            'endpoints' => []
        ];
        
        // 更新总体统计
        $stats['total_requests']++;
        $stats['total_response_time'] += $data['response_time'];
        $stats['total_memory_usage'] += $data['memory_usage'];
        
        if ($data['status_code'] >= 400) {
            $stats['error_count']++;
        }
        
        if ($data['response_time'] > 1000) {
            $stats['slow_request_count']++;
        }
        
        // 更新端点统计
        $endpoint = $data['method'] . ' ' . $data['endpoint'];
        if (!isset($stats['endpoints'][$endpoint])) {
            $stats['endpoints'][$endpoint] = [
                'count' => 0,
                'total_time' => 0,
                'total_memory' => 0,
                'errors' => 0,
                'min_time' => PHP_INT_MAX,
                'max_time' => 0
            ];
        }
        
        $endpointStats = &$stats['endpoints'][$endpoint];
        $endpointStats['count']++;
        $endpointStats['total_time'] += $data['response_time'];
        $endpointStats['total_memory'] += $data['memory_usage'];
        $endpointStats['min_time'] = min($endpointStats['min_time'], $data['response_time']);
        $endpointStats['max_time'] = max($endpointStats['max_time'], $data['response_time']);
        
        if ($data['status_code'] >= 400) {
            $endpointStats['errors']++;
        }
        
        // 缓存统计数据（1小时）
        cache($cacheKey, $stats, 3600);
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 获取性能报告
     */
    public static function getPerformanceReport($date = null, $hour = null)
    {
        $date = $date ?: date('Y-m-d');
        
        if ($hour !== null) {
            // 获取特定小时的数据
            $cacheKey = 'perf_stats_' . $date . '-' . str_pad($hour, 2, '0', STR_PAD_LEFT);
            return cache($cacheKey) ?: [];
        }
        
        // 获取整天的数据
        $dayStats = [
            'date' => $date,
            'total_requests' => 0,
            'total_response_time' => 0,
            'total_memory_usage' => 0,
            'error_count' => 0,
            'slow_request_count' => 0,
            'hourly_stats' => [],
            'top_endpoints' => []
        ];
        
        $allEndpoints = [];
        
        for ($h = 0; $h < 24; $h++) {
            $hourKey = $date . '-' . str_pad($h, 2, '0', STR_PAD_LEFT);
            $hourStats = cache('perf_stats_' . $hourKey) ?: [];
            
            if (!empty($hourStats)) {
                $dayStats['total_requests'] += $hourStats['total_requests'];
                $dayStats['total_response_time'] += $hourStats['total_response_time'];
                $dayStats['total_memory_usage'] += $hourStats['total_memory_usage'];
                $dayStats['error_count'] += $hourStats['error_count'];
                $dayStats['slow_request_count'] += $hourStats['slow_request_count'];
                
                $dayStats['hourly_stats'][$h] = [
                    'hour' => $h,
                    'requests' => $hourStats['total_requests'],
                    'avg_response_time' => $hourStats['total_requests'] > 0 
                        ? round($hourStats['total_response_time'] / $hourStats['total_requests'], 2) 
                        : 0,
                    'error_rate' => $hourStats['total_requests'] > 0 
                        ? round($hourStats['error_count'] / $hourStats['total_requests'] * 100, 2) 
                        : 0
                ];
                
                // 合并端点统计
                if (isset($hourStats['endpoints'])) {
                    foreach ($hourStats['endpoints'] as $endpoint => $stats) {
                        if (!isset($allEndpoints[$endpoint])) {
                            $allEndpoints[$endpoint] = [
                                'count' => 0,
                                'total_time' => 0,
                                'total_memory' => 0,
                                'errors' => 0,
                                'min_time' => PHP_INT_MAX,
                                'max_time' => 0
                            ];
                        }
                        
                        $allEndpoints[$endpoint]['count'] += $stats['count'];
                        $allEndpoints[$endpoint]['total_time'] += $stats['total_time'];
                        $allEndpoints[$endpoint]['total_memory'] += $stats['total_memory'];
                        $allEndpoints[$endpoint]['errors'] += $stats['errors'];
                        $allEndpoints[$endpoint]['min_time'] = min($allEndpoints[$endpoint]['min_time'], $stats['min_time']);
                        $allEndpoints[$endpoint]['max_time'] = max($allEndpoints[$endpoint]['max_time'], $stats['max_time']);
                    }
                }
            }
        }
        
        // 计算平均值
        if ($dayStats['total_requests'] > 0) {
            $dayStats['avg_response_time'] = round($dayStats['total_response_time'] / $dayStats['total_requests'], 2);
            $dayStats['error_rate'] = round($dayStats['error_count'] / $dayStats['total_requests'] * 100, 2);
            $dayStats['slow_request_rate'] = round($dayStats['slow_request_count'] / $dayStats['total_requests'] * 100, 2);
        }
        
        // 排序并获取Top端点
        foreach ($allEndpoints as $endpoint => $stats) {
            $stats['avg_time'] = $stats['count'] > 0 ? round($stats['total_time'] / $stats['count'], 2) : 0;
            $stats['error_rate'] = $stats['count'] > 0 ? round($stats['errors'] / $stats['count'] * 100, 2) : 0;
            $stats['endpoint'] = $endpoint;
            $dayStats['top_endpoints'][] = $stats;
        }
        
        // 按请求数排序
        usort($dayStats['top_endpoints'], function($a, $b) {
            return $b['count'] - $a['count'];
        });
        
        // 只保留前20个
        $dayStats['top_endpoints'] = array_slice($dayStats['top_endpoints'], 0, 20);
        
        return $dayStats;
    }
}
