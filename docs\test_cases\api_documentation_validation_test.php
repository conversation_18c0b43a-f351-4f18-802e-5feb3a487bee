<?php
/**
 * API文档完整性验证测试脚本
 * 验证API文档的完整性、准确性和可用性
 */

echo "开始验证API文档完整性\n";
echo "====================================\n";

// 测试结果统计
$test_results = [
    'total' => 0,
    'passed' => 0,
    'failed' => 0
];

function log_test_result($test_name, $success, $message = '', $details = []) {
    global $test_results;
    
    $test_results['total']++;
    
    if ($success) {
        $test_results['passed']++;
        echo "✓ PASS: $test_name - $message\n";
    } else {
        $test_results['failed']++;
        echo "✗ FAIL: $test_name - $message\n";
        
        if (!empty($details)) {
            foreach ($details as $detail) {
                echo "  - $detail\n";
            }
        }
    }
}

/**
 * 测试API文档文件存在性
 */
function test_api_documentation_files() {
    echo "\n=== 测试API文档文件 ===\n";
    
    $docFiles = [
        __DIR__ . '/../api/模板商品化系统API文档.md' => 'API文档主文件',
        __DIR__ . '/../api/模板商品化系统API_Postman集合.json' => 'Postman集合文件'
    ];
    
    foreach ($docFiles as $file => $desc) {
        $exists = file_exists($file);
        log_test_result('文档文件', $exists, $exists ? "$desc 存在" : "$desc 缺失");
        
        if ($exists) {
            $content = file_get_contents($file);
            $size = strlen($content);
            $hasContent = $size > 5000; // 至少5KB内容
            log_test_result('文档内容', $hasContent, $hasContent ? "$desc 内容充实($size bytes)" : "$desc 内容不足($size bytes)");
        }
    }
}

/**
 * 测试API文档结构完整性
 */
function test_api_documentation_structure() {
    echo "\n=== 测试API文档结构 ===\n";
    
    $docFile = __DIR__ . '/../api/模板商品化系统API文档.md';
    if (!file_exists($docFile)) {
        log_test_result('文档结构', false, 'API文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查文档基本结构
    $basicStructures = [
        '# 模板商品化系统API文档' => '文档标题',
        '## 概述' => '概述章节',
        '## 基础信息' => '基础信息章节',
        '## 通用响应格式' => '响应格式章节',
        '## 错误码说明' => '错误码章节',
        '## 认证说明' => '认证章节',
        '## API接口列表' => 'API列表章节'
    ];
    
    foreach ($basicStructures as $structure => $desc) {
        $hasStructure = strpos($content, $structure) !== false;
        log_test_result('文档结构', $hasStructure, $hasStructure ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查API接口章节
    $apiSections = [
        '### 1. 参数填写相关接口' => '参数填写接口章节',
        '### 2. 服务订单相关接口' => '服务订单接口章节',
        '### 3. 团购相关接口' => '团购接口章节',
        '### 4. 商品相关接口' => '商品接口章节',
        '### 5. 用户相关接口' => '用户接口章节',
        '### 6. 文件上传接口' => '文件上传接口章节'
    ];
    
    foreach ($apiSections as $section => $desc) {
        $hasSection = strpos($content, $section) !== false;
        log_test_result('API章节', $hasSection, $hasSection ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查示例代码
    $codeExamples = [
        '## SDK使用示例' => 'SDK示例章节',
        '### JavaScript/UniApp示例' => 'JavaScript示例',
        '### PHP示例' => 'PHP示例',
        '## 最佳实践' => '最佳实践章节',
        '## 常见问题' => '常见问题章节'
    ];
    
    foreach ($codeExamples as $example => $desc) {
        $hasExample = strpos($content, $example) !== false;
        log_test_result('示例代码', $hasExample, $hasExample ? "$desc 存在" : "$desc 缺失");
    }
}

/**
 * 测试API接口完整性
 */
function test_api_endpoints_completeness() {
    echo "\n=== 测试API接口完整性 ===\n";
    
    $docFile = __DIR__ . '/../api/模板商品化系统API文档.md';
    if (!file_exists($docFile)) {
        log_test_result('接口完整性', false, 'API文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查核心API接口
    $coreEndpoints = [
        'POST /api/parameter_session/create' => '创建参数填写会话',
        'PUT /api/parameter_session/{session_id}' => '更新参数值',
        'GET /api/parameter_session/{session_id}' => '获取会话详情',
        'POST /api/parameter_session/{session_id}/preview' => '生成预览',
        'POST /api/parameter_session/{session_id}/complete' => '完成填写',
        'POST /api/service_order/create' => '创建服务订单',
        'GET /api/service_order/{order_id}/status' => '获取订单状态',
        'GET /api/service_order/{order_id}/delivery' => '获取交付信息',
        'POST /api/service_order/{order_id}/download' => '下载生成文件',
        'GET /api/service_order/lists' => '获取用户订单列表',
        'POST /api/service_order/{order_id}/cancel' => '取消订单',
        'GET /api/group_buy/lists' => '获取团购活动列表',
        'GET /api/group_buy/{activity_id}' => '获取团购活动详情',
        'POST /api/group_buy/{activity_id}/join' => '参与团购',
        'GET /api/group_buy/my_records' => '获取用户团购记录'
    ];
    
    foreach ($coreEndpoints as $endpoint => $desc) {
        $hasEndpoint = strpos($content, $endpoint) !== false;
        log_test_result('API接口', $hasEndpoint, $hasEndpoint ? "$desc 接口存在" : "$desc 接口缺失");
    }
    
    // 检查请求参数示例
    $parameterExamples = [
        '"goods_id": 123' => '商品ID参数',
        '"parameter_values"' => '参数值对象',
        '"session_id"' => '会话ID参数',
        '"address_id"' => '地址ID参数',
        '"print_options"' => '打印选项参数',
        '"payment_method"' => '支付方式参数'
    ];
    
    foreach ($parameterExamples as $param => $desc) {
        $hasParam = strpos($content, $param) !== false;
        log_test_result('请求参数', $hasParam, $hasParam ? "$desc 示例存在" : "$desc 示例缺失");
    }
    
    // 检查响应示例
    $responseExamples = [
        '"code": 1' => '成功响应码',
        '"code": 0' => '失败响应码',
        '"msg":' => '响应消息',
        '"data":' => '响应数据',
        '"timestamp":' => '时间戳'
    ];
    
    foreach ($responseExamples as $response => $desc) {
        $hasResponse = strpos($content, $response) !== false;
        log_test_result('响应示例', $hasResponse, $hasResponse ? "$desc 示例存在" : "$desc 示例缺失");
    }
}

/**
 * 测试Postman集合完整性
 */
function test_postman_collection_completeness() {
    echo "\n=== 测试Postman集合完整性 ===\n";
    
    $postmanFile = __DIR__ . '/../api/模板商品化系统API_Postman集合.json';
    if (!file_exists($postmanFile)) {
        log_test_result('Postman集合', false, 'Postman集合文件不存在');
        return;
    }
    
    $content = file_get_contents($postmanFile);
    $collection = json_decode($content, true);
    
    if (!$collection) {
        log_test_result('Postman格式', false, 'Postman集合JSON格式错误');
        return;
    }
    
    // 检查集合基本信息
    $basicInfo = [
        'info' => '集合信息',
        'auth' => '认证配置',
        'variable' => '变量配置',
        'item' => '接口项目'
    ];
    
    foreach ($basicInfo as $key => $desc) {
        $hasKey = isset($collection[$key]);
        log_test_result('Postman结构', $hasKey, $hasKey ? "$desc 存在" : "$desc 缺失");
    }
    
    // 检查接口分组
    if (isset($collection['item'])) {
        $expectedGroups = [
            '1. 参数填写相关接口',
            '2. 服务订单相关接口',
            '3. 团购相关接口',
            '4. 商品相关接口',
            '5. 用户相关接口',
            '6. 文件上传接口'
        ];
        
        $actualGroups = array_column($collection['item'], 'name');
        
        foreach ($expectedGroups as $group) {
            $hasGroup = in_array($group, $actualGroups);
            log_test_result('接口分组', $hasGroup, $hasGroup ? "$group 分组存在" : "$group 分组缺失");
        }
        
        // 统计接口总数
        $totalEndpoints = 0;
        foreach ($collection['item'] as $group) {
            if (isset($group['item'])) {
                $totalEndpoints += count($group['item']);
            }
        }
        
        $hasEnoughEndpoints = $totalEndpoints >= 15;
        log_test_result('接口数量', $hasEnoughEndpoints, $hasEnoughEndpoints ? "包含$totalEndpoints个接口" : "接口数量不足($totalEndpoints个)");
    }
    
    // 检查变量配置
    if (isset($collection['variable'])) {
        $expectedVars = ['base_url', 'access_token', 'session_id', 'order_id', 'activity_id'];
        $actualVars = array_column($collection['variable'], 'key');
        
        foreach ($expectedVars as $var) {
            $hasVar = in_array($var, $actualVars);
            log_test_result('Postman变量', $hasVar, $hasVar ? "$var 变量存在" : "$var 变量缺失");
        }
    }
}

/**
 * 测试文档内容质量
 */
function test_documentation_quality() {
    echo "\n=== 测试文档内容质量 ===\n";
    
    $docFile = __DIR__ . '/../api/模板商品化系统API文档.md';
    if (!file_exists($docFile)) {
        log_test_result('文档质量', false, 'API文档文件不存在');
        return;
    }
    
    $content = file_get_contents($docFile);
    
    // 检查代码块数量
    $codeBlockCount = substr_count($content, '```');
    $hasEnoughCodeBlocks = $codeBlockCount >= 20;
    log_test_result('代码示例', $hasEnoughCodeBlocks, $hasEnoughCodeBlocks ? "包含$codeBlockCount个代码块" : "代码示例不足($codeBlockCount个)");
    
    // 检查JSON示例
    $jsonExampleCount = substr_count($content, '```json');
    $hasEnoughJsonExamples = $jsonExampleCount >= 15;
    log_test_result('JSON示例', $hasEnoughJsonExamples, $hasEnoughJsonExamples ? "包含$jsonExampleCount个JSON示例" : "JSON示例不足($jsonExampleCount个)");
    
    // 检查表格数量
    $tableCount = substr_count($content, '|');
    $hasEnoughTables = $tableCount >= 10;
    log_test_result('表格内容', $hasEnoughTables, $hasEnoughTables ? "包含表格内容" : "表格内容不足");
    
    // 检查链接和引用
    $linkCount = substr_count($content, 'http');
    $hasLinks = $linkCount > 0;
    log_test_result('链接引用', $hasLinks, $hasLinks ? "包含$linkCount个链接" : "缺少链接引用");
    
    // 检查中文内容完整性
    $chineseContentChecks = [
        '概述' => '中文概述',
        '基础信息' => '中文基础信息',
        '错误码说明' => '中文错误码说明',
        '认证说明' => '中文认证说明',
        '最佳实践' => '中文最佳实践',
        '常见问题' => '中文常见问题'
    ];
    
    foreach ($chineseContentChecks as $check => $desc) {
        $hasCheck = strpos($content, $check) !== false;
        log_test_result('中文内容', $hasCheck, $hasCheck ? "$desc 存在" : "$desc 缺失");
    }
}

/**
 * 测试API文档与实际接口的一致性
 */
function test_api_consistency() {
    echo "\n=== 测试API文档一致性 ===\n";
    
    // 检查控制器文件是否存在
    $controllerFiles = [
        __DIR__ . '/../../application/api/controller/ParameterSession.php' => 'ParameterSession控制器',
        __DIR__ . '/../../application/api/controller/ServiceOrder.php' => 'ServiceOrder控制器',
        __DIR__ . '/../../application/api/controller/GroupBuy.php' => 'GroupBuy控制器'
    ];
    
    $existingControllers = 0;
    foreach ($controllerFiles as $file => $desc) {
        $exists = file_exists($file);
        if ($exists) {
            $existingControllers++;
        }
        log_test_result('控制器文件', $exists, $exists ? "$desc 存在" : "$desc 缺失");
    }
    
    $consistencyRate = round(($existingControllers / count($controllerFiles)) * 100, 1);
    log_test_result('一致性评估', $consistencyRate >= 70, "API文档与实际实现一致性: $consistencyRate%");
}

// 执行所有测试
test_api_documentation_files();
test_api_documentation_structure();
test_api_endpoints_completeness();
test_postman_collection_completeness();
test_documentation_quality();
test_api_consistency();

// 输出测试结果统计
echo "\n====================================\n";
echo "API文档验证完成\n";
echo "总计: {$test_results['total']} 个测试项\n";
echo "通过: {$test_results['passed']} 个测试项\n";
echo "失败: {$test_results['failed']} 个测试项\n";

$success_rate = round(($test_results['passed'] / $test_results['total']) * 100, 1);
echo "验证通过率: $success_rate%\n";

if ($success_rate >= 95) {
    echo "✓ API文档质量优秀！文档完整、准确、易用。\n";
    exit(0);
} elseif ($success_rate >= 85) {
    echo "? API文档质量良好，但需要完善部分内容。\n";
    exit(1);
} else {
    echo "✗ API文档质量需要改进，请完善缺失的内容。\n";
    exit(2);
}
?>
