{"info": {"name": "模板商品化系统API", "description": "模板商品化系统完整API接口集合，包含参数填写、订单创建、团购参与等功能", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "https://api.yoursite.com", "type": "string"}, {"key": "access_token", "value": "your_access_token_here", "type": "string"}, {"key": "session_id", "value": "", "type": "string"}, {"key": "order_id", "value": "", "type": "string"}, {"key": "activity_id", "value": "", "type": "string"}], "item": [{"name": "1. 参数填写相关接口", "item": [{"name": "创建参数填写会话", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"goods_id\": 123\n}"}, "url": {"raw": "{{base_url}}/api/parameter_session/create", "host": ["{{base_url}}"], "path": ["api", "parameter_session", "create"]}}, "response": []}, {"name": "更新参数值", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"parameter_values\": {\n    \"title\": \"我的专属海报\",\n    \"subtitle\": \"专业定制服务\",\n    \"background_color\": \"#ff6b6b\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/parameter_session/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "parameter_session", "{{session_id}}"]}}, "response": []}, {"name": "获取会话详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/parameter_session/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "parameter_session", "{{session_id}}"]}}, "response": []}, {"name": "生成预览", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"preview_options\": {\n    \"width\": 800,\n    \"height\": 600,\n    \"quality\": 0.8\n  }\n}"}, "url": {"raw": "{{base_url}}/api/parameter_session/{{session_id}}/preview", "host": ["{{base_url}}"], "path": ["api", "parameter_session", "{{session_id}}", "preview"]}}, "response": []}, {"name": "完成填写", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/parameter_session/{{session_id}}/complete", "host": ["{{base_url}}"], "path": ["api", "parameter_session", "{{session_id}}", "complete"]}}, "response": []}]}, {"name": "2. 服务订单相关接口", "item": [{"name": "创建服务订单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"address_id\": 123,\n  \"print_options\": {\n    \"material\": \"premium_paper\",\n    \"size\": \"A4\",\n    \"quantity\": 1\n  },\n  \"payment_method\": \"wechat\",\n  \"remark\": \"请尽快处理\"\n}"}, "url": {"raw": "{{base_url}}/api/service_order/create", "host": ["{{base_url}}"], "path": ["api", "service_order", "create"]}}, "response": []}, {"name": "获取订单状态", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/service_order/{{order_id}}/status", "host": ["{{base_url}}"], "path": ["api", "service_order", "{{order_id}}", "status"]}}, "response": []}, {"name": "获取交付信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/service_order/{{order_id}}/delivery", "host": ["{{base_url}}"], "path": ["api", "service_order", "{{order_id}}", "delivery"]}}, "response": []}, {"name": "下载生成文件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/service_order/{{order_id}}/download", "host": ["{{base_url}}"], "path": ["api", "service_order", "{{order_id}}", "download"]}}, "response": []}, {"name": "获取用户订单列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/service_order/lists?page=1&limit=10&status=all&sort=create_time_desc", "host": ["{{base_url}}"], "path": ["api", "service_order", "lists"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "all"}, {"key": "sort", "value": "create_time_desc"}]}}, "response": []}, {"name": "取消订单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cancel_reason\": \"不需要了\"\n}"}, "url": {"raw": "{{base_url}}/api/service_order/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["api", "service_order", "{{order_id}}", "cancel"]}}, "response": []}]}, {"name": "3. 团购相关接口", "item": [{"name": "获取团购活动列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/group_buy/lists?page=1&limit=10&status=active&sort=create_time_desc", "host": ["{{base_url}}"], "path": ["api", "group_buy", "lists"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "active"}, {"key": "sort", "value": "create_time_desc"}]}}, "response": []}, {"name": "获取团购活动详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/group_buy/{{activity_id}}", "host": ["{{base_url}}"], "path": ["api", "group_buy", "{{activity_id}}"]}}, "response": []}, {"name": "参与团购", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"address_id\": 123,\n  \"print_options\": {\n    \"material\": \"premium_paper\",\n    \"size\": \"A4\",\n    \"quantity\": 1\n  },\n  \"payment_method\": \"wechat\"\n}"}, "url": {"raw": "{{base_url}}/api/group_buy/{{activity_id}}/join", "host": ["{{base_url}}"], "path": ["api", "group_buy", "{{activity_id}}", "join"]}}, "response": []}, {"name": "获取用户团购记录", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/group_buy/my_records?page=1&limit=10&status=all", "host": ["{{base_url}}"], "path": ["api", "group_buy", "my_records"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "all"}]}}, "response": []}]}, {"name": "4. 商品相关接口", "item": [{"name": "获取服务商品列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/goods/service_list?page=1&limit=20&keyword=海报", "host": ["{{base_url}}"], "path": ["api", "goods", "service_list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "keyword", "value": "海报"}]}}, "response": []}, {"name": "获取商品详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/goods/123", "host": ["{{base_url}}"], "path": ["api", "goods", "123"]}}, "response": []}]}, {"name": "5. 用户相关接口", "item": [{"name": "获取用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user/info", "host": ["{{base_url}}"], "path": ["api", "user", "info"]}}, "response": []}, {"name": "获取用户地址列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user/address", "host": ["{{base_url}}"], "path": ["api", "user", "address"]}}, "response": []}]}, {"name": "6. 文件上传接口", "item": [{"name": "上传图片", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "type", "value": "parameter", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/upload/image", "host": ["{{base_url}}"], "path": ["api", "upload", "image"]}}, "response": []}]}]}