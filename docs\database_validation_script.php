<?php
/**
 * 模板商品化系统数据库结构验证脚本
 * 自动验证数据库迁移状态和表结构完整性
 * 
 * 使用方法：
 * php database_validation_script.php
 * 
 * 或在浏览器中访问：
 * http://your-domain/docs/database_validation_script.php
 */

// 数据库配置 - 请根据实际情况修改
$config = [
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'likeshop',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

// 验证结果存储
$results = [
    'total_checks' => 0,
    'passed_checks' => 0,
    'failed_checks' => 0,
    'details' => []
];

/**
 * 数据库连接
 */
function connectDatabase($config) {
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("数据库连接失败: " . $e->getMessage());
    }
}

/**
 * 记录验证结果
 */
function recordResult($category, $item, $status, $message, $expected = null, $actual = null) {
    global $results;
    
    $results['total_checks']++;
    if ($status) {
        $results['passed_checks']++;
    } else {
        $results['failed_checks']++;
    }
    
    $results['details'][] = [
        'category' => $category,
        'item' => $item,
        'status' => $status ? 'PASS' : 'FAIL',
        'message' => $message,
        'expected' => $expected,
        'actual' => $actual,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

/**
 * 验证商品表扩展字段
 */
function validateGoodsTableExtension($pdo) {
    $expectedFields = [
        'goods_type' => ['type' => 'tinyint', 'null' => 'YES', 'default' => '1'],
        'template_config_id' => ['type' => 'varchar', 'null' => 'YES', 'default' => null],
        'service_delivery_time' => ['type' => 'int', 'null' => 'YES', 'default' => '72'],
        'print_materials' => ['type' => 'json', 'null' => 'YES', 'default' => null],
        'print_sizes' => ['type' => 'json', 'null' => 'YES', 'default' => null],
        'support_group_buy' => ['type' => 'tinyint', 'null' => 'YES', 'default' => '0'],
        'group_buy_min_count' => ['type' => 'int', 'null' => 'YES', 'default' => '2'],
        'group_buy_price' => ['type' => 'decimal', 'null' => 'YES', 'default' => '0.00'],
        'group_buy_duration' => ['type' => 'int', 'null' => 'YES', 'default' => '24']
    ];
    
    try {
        $stmt = $pdo->prepare("
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
            FROM information_schema.COLUMNS 
            WHERE table_schema = DATABASE() 
            AND table_name = 'ls_goods' 
            AND COLUMN_NAME IN ('" . implode("','", array_keys($expectedFields)) . "')
        ");
        $stmt->execute();
        $actualFields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $foundFields = [];
        foreach ($actualFields as $field) {
            $foundFields[$field['COLUMN_NAME']] = [
                'type' => strtolower($field['DATA_TYPE']),
                'null' => $field['IS_NULLABLE'],
                'default' => $field['COLUMN_DEFAULT']
            ];
        }
        
        foreach ($expectedFields as $fieldName => $expectedProps) {
            if (isset($foundFields[$fieldName])) {
                $actualProps = $foundFields[$fieldName];
                $typeMatch = strpos($actualProps['type'], $expectedProps['type']) !== false;
                
                if ($typeMatch) {
                    recordResult('商品表扩展', $fieldName, true, "字段存在且类型正确");
                } else {
                    recordResult('商品表扩展', $fieldName, false, "字段类型不匹配", 
                        $expectedProps['type'], $actualProps['type']);
                }
            } else {
                recordResult('商品表扩展', $fieldName, false, "字段不存在");
            }
        }
        
        // 验证索引
        $stmt = $pdo->prepare("
            SHOW INDEX FROM ls_goods 
            WHERE Key_name IN ('idx_goods_type', 'idx_template_config')
        ");
        $stmt->execute();
        $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $indexNames = array_column($indexes, 'Key_name');
        $expectedIndexes = ['idx_goods_type', 'idx_template_config'];
        
        foreach ($expectedIndexes as $indexName) {
            if (in_array($indexName, $indexNames)) {
                recordResult('商品表索引', $indexName, true, "索引存在");
            } else {
                recordResult('商品表索引', $indexName, false, "索引不存在");
            }
        }
        
    } catch (PDOException $e) {
        recordResult('商品表扩展', '查询异常', false, "查询失败: " . $e->getMessage());
    }
}

/**
 * 验证新核心表创建
 */
function validateNewTables($pdo) {
    $expectedTables = [
        'ls_parameter_fill_sessions' => '参数填写会话表',
        'ls_template_order_params' => '模板订单参数表',
        'ls_service_delivery_records' => '服务交付记录表',
        'ls_group_buy_activities' => '团购活动表',
        'ls_group_buy_participants' => '团购参与记录表',
        'ls_file_download_logs' => '文件下载日志表'
    ];
    
    try {
        $stmt = $pdo->prepare("
            SELECT table_name, table_comment 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name IN ('" . implode("','", array_keys($expectedTables)) . "')
        ");
        $stmt->execute();
        $actualTables = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        foreach ($expectedTables as $tableName => $description) {
            if (isset($actualTables[$tableName])) {
                recordResult('新核心表', $tableName, true, "表存在: {$description}");
                
                // 验证表结构完整性
                validateTableStructure($pdo, $tableName);
            } else {
                recordResult('新核心表', $tableName, false, "表不存在: {$description}");
            }
        }
        
    } catch (PDOException $e) {
        recordResult('新核心表', '查询异常', false, "查询失败: " . $e->getMessage());
    }
}

/**
 * 验证表结构完整性
 */
function validateTableStructure($pdo, $tableName) {
    $requiredFields = [
        'ls_parameter_fill_sessions' => ['id', 'user_id', 'goods_id', 'template_config_id', 'parameter_values', 'is_completed'],
        'ls_template_order_params' => ['id', 'order_id', 'session_id', 'parameter_values'],
        'ls_service_delivery_records' => ['id', 'order_id', 'order_goods_id', 'delivery_status'],
        'ls_group_buy_activities' => ['id', 'goods_id', 'admin_user_id', 'activity_title', 'min_participants'],
        'ls_group_buy_participants' => ['id', 'activity_id', 'user_id', 'session_id'],
        'ls_file_download_logs' => ['id', 'user_id', 'file_path', 'download_time']
    ];
    
    if (!isset($requiredFields[$tableName])) {
        return;
    }
    
    try {
        $stmt = $pdo->prepare("DESCRIBE {$tableName}");
        $stmt->execute();
        $fields = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missingFields = array_diff($requiredFields[$tableName], $fields);
        
        if (empty($missingFields)) {
            recordResult('表结构', $tableName, true, "所有必需字段存在");
        } else {
            recordResult('表结构', $tableName, false, "缺少字段: " . implode(', ', $missingFields));
        }
        
    } catch (PDOException $e) {
        recordResult('表结构', $tableName, false, "结构验证失败: " . $e->getMessage());
    }
}

/**
 * 验证外键约束
 */
function validateForeignKeys($pdo) {
    $expectedConstraints = [
        'ls_group_buy_participants' => [
            'fk_participants_activity' => 'activity_id references ls_group_buy_activities(id)'
        ],
        'ls_template_order_params' => [
            'fk_params_order' => 'order_id references ls_order(id)'
        ]
    ];
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                TABLE_NAME,
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE table_schema = DATABASE() 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $stmt->execute();
        $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $foundConstraints = [];
        foreach ($constraints as $constraint) {
            $foundConstraints[$constraint['TABLE_NAME']][] = $constraint['CONSTRAINT_NAME'];
        }
        
        foreach ($expectedConstraints as $tableName => $tableConstraints) {
            foreach ($tableConstraints as $constraintName => $description) {
                if (isset($foundConstraints[$tableName]) && 
                    in_array($constraintName, $foundConstraints[$tableName])) {
                    recordResult('外键约束', "{$tableName}.{$constraintName}", true, "约束存在");
                } else {
                    recordResult('外键约束', "{$tableName}.{$constraintName}", false, "约束不存在: {$description}");
                }
            }
        }
        
    } catch (PDOException $e) {
        recordResult('外键约束', '查询异常', false, "查询失败: " . $e->getMessage());
    }
}

/**
 * 验证数据完整性
 */
function validateDataIntegrity($pdo) {
    $checks = [
        [
            'name' => '商品表数据一致性',
            'sql' => "SELECT COUNT(*) FROM ls_goods WHERE goods_type = 2 AND template_config_id IS NULL",
            'expected' => 0,
            'description' => '服务商品必须关联模板配置'
        ],
        [
            'name' => '团购活动数据一致性', 
            'sql' => "SELECT COUNT(*) FROM ls_group_buy_activities WHERE min_participants <= 0",
            'expected' => 0,
            'description' => '最小成团人数必须大于0'
        ],
        [
            'name' => '参数会话数据一致性',
            'sql' => "SELECT COUNT(*) FROM ls_parameter_fill_sessions WHERE expires_at < created_at",
            'expected' => 0,
            'description' => '过期时间不能早于创建时间'
        ]
    ];
    
    foreach ($checks as $check) {
        try {
            $stmt = $pdo->prepare($check['sql']);
            $stmt->execute();
            $actual = $stmt->fetchColumn();
            
            if ($actual == $check['expected']) {
                recordResult('数据完整性', $check['name'], true, $check['description']);
            } else {
                recordResult('数据完整性', $check['name'], false, 
                    $check['description'], $check['expected'], $actual);
            }
            
        } catch (PDOException $e) {
            recordResult('数据完整性', $check['name'], false, "检查失败: " . $e->getMessage());
        }
    }
}

/**
 * 生成验证报告
 */
function generateReport($results) {
    $passRate = $results['total_checks'] > 0 ? 
        round(($results['passed_checks'] / $results['total_checks']) * 100, 2) : 0;
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "模板商品化系统数据库结构验证报告\n";
    echo str_repeat("=", 80) . "\n";
    echo "验证时间: " . date('Y-m-d H:i:s') . "\n";
    echo "总检查项: {$results['total_checks']}\n";
    echo "通过检查: {$results['passed_checks']}\n";
    echo "失败检查: {$results['failed_checks']}\n";
    echo "通过率: {$passRate}%\n";
    echo str_repeat("-", 80) . "\n\n";
    
    // 按类别分组显示结果
    $categories = [];
    foreach ($results['details'] as $detail) {
        $categories[$detail['category']][] = $detail;
    }
    
    foreach ($categories as $category => $items) {
        echo "【{$category}】\n";
        foreach ($items as $item) {
            $status = $item['status'] == 'PASS' ? '✓' : '✗';
            echo "  {$status} {$item['item']}: {$item['message']}\n";
            
            if ($item['status'] == 'FAIL' && $item['expected'] && $item['actual']) {
                echo "    期望值: {$item['expected']}, 实际值: {$item['actual']}\n";
            }
        }
        echo "\n";
    }
    
    // 总体评估
    echo str_repeat("=", 80) . "\n";
    if ($passRate >= 95) {
        echo "✓ 验证结果: 优秀 - 数据库结构完整，可以正常使用\n";
    } elseif ($passRate >= 80) {
        echo "⚠ 验证结果: 良好 - 存在少量问题，建议修复后使用\n";
    } elseif ($passRate >= 60) {
        echo "⚠ 验证结果: 一般 - 存在较多问题，需要修复后使用\n";
    } else {
        echo "✗ 验证结果: 不合格 - 存在严重问题，不建议使用\n";
    }
    echo str_repeat("=", 80) . "\n";
}

// 主执行流程
try {
    echo "开始验证模板商品化系统数据库结构...\n\n";
    
    // 连接数据库
    $pdo = connectDatabase($config);
    echo "✓ 数据库连接成功\n\n";
    
    // 执行各项验证
    echo "正在验证商品表扩展...\n";
    validateGoodsTableExtension($pdo);
    
    echo "正在验证新核心表...\n";
    validateNewTables($pdo);
    
    echo "正在验证外键约束...\n";
    validateForeignKeys($pdo);
    
    echo "正在验证数据完整性...\n";
    validateDataIntegrity($pdo);
    
    // 生成报告
    generateReport($results);
    
} catch (Exception $e) {
    echo "验证过程中发生错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
