<?php
/**
 * 参数填写API接口集成测试脚本
 * 用于验证ParameterSession API控制器的实际功能
 */

// 设置测试环境
define('APP_PATH', __DIR__ . '/../../application/');
define('ROOT_PATH', __DIR__ . '/../../');

// 模拟ThinkPHP环境
if (!class_exists('think\Db')) {
    // 创建模拟的Db类
    class MockDb {
        private static $data = [];
        private static $lastTable = '';
        
        public static function name($table) {
            self::$lastTable = $table;
            return new self();
        }
        
        public function alias($alias) {
            return $this;
        }
        
        public function leftJoin($table, $condition) {
            return $this;
        }
        
        public function where($conditions) {
            return $this;
        }
        
        public function field($fields) {
            return $this;
        }
        
        public function order($order) {
            return $this;
        }
        
        public function find() {
            // 模拟数据库查询结果
            if (self::$lastTable == 'goods') {
                return [
                    'id' => 123,
                    'name' => '海报定制服务',
                    'goods_type' => 2,
                    'service_config' => json_encode([
                        'template_config_id' => 'template_001',
                        'delivery_time' => 72
                    ]),
                    'del' => 0,
                    'status' => 1,
                    'price' => 29.90,
                    'image' => '/uploads/goods/poster.jpg'
                ];
            }
            
            return null;
        }
        
        public function select() {
            return [];
        }
        
        public function paginate($limit, $simple = false, $config = []) {
            return new MockPaginator();
        }
        
        public function insert($data) {
            return true;
        }
        
        public function insertGetId($data) {
            return 1001;
        }
        
        public function update($data) {
            return true;
        }
        
        public function delete() {
            return true;
        }
        
        public function count($field = '*') {
            return 10;
        }
        
        public static function startTrans() {
            return true;
        }
        
        public static function commit() {
            return true;
        }
        
        public static function rollback() {
            return true;
        }
    }
    
    class MockPaginator {
        public function items() {
            return [
                [
                    'id' => 'session_001',
                    'user_id' => 1001,
                    'goods_id' => 123,
                    'goods_name' => '海报定制服务',
                    'goods_image' => '/uploads/goods/poster.jpg',
                    'goods_price' => 29.90,
                    'completion_rate' => 75.0,
                    'is_completed' => 0,
                    'expires_at' => date('Y-m-d H:i:s', time() + 18*3600),
                    'created_at' => date('Y-m-d H:i:s', time() - 6*3600),
                    'parameter_values' => json_encode(['title' => '我的海报'])
                ]
            ];
        }
        
        public function total() {
            return 1;
        }
    }
    
    // 创建命名空间别名
    class_alias('MockDb', 'think\Db');
}

// 模拟Exception类
if (!class_exists('think\Exception')) {
    class_alias('Exception', 'think\Exception');
}

// 模拟Request类
if (!class_exists('think\Request')) {
    class MockRequest {
        private $data = [];
        
        public function __construct($data = []) {
            $this->data = $data;
        }
        
        public function post($key = '', $default = null) {
            if (empty($key)) {
                return $this->data['post'] ?? [];
            }
            return $this->data['post'][$key] ?? $default;
        }
        
        public function get($key = '', $default = null) {
            if (empty($key)) {
                return $this->data['get'] ?? [];
            }
            return $this->data['get'][$key] ?? $default;
        }
        
        public function param($key = '', $default = null) {
            if (empty($key)) {
                return array_merge($this->data['get'] ?? [], $this->data['post'] ?? []);
            }
            return $this->data['param'][$key] ?? $this->data['get'][$key] ?? $this->data['post'][$key] ?? $default;
        }
    }
    
    class_alias('MockRequest', 'think\Request');
}

// 创建命名空间目录
if (!is_dir(__DIR__ . '/../../application/api/controller')) {
    mkdir(__DIR__ . '/../../application/api/controller', 0755, true);
}

// 创建模拟的ApiBase文件
file_put_contents(__DIR__ . '/../../application/api/controller/ApiBase.php', '<?php
namespace app\api\controller;

class ApiBase {
    public $user_id = 1001;
    public $request;

    public function __construct($requestData = []) {
        $this->request = new \think\Request($requestData);
    }

    public function success($msg = "", $data = []) {
        return ["code" => 1, "msg" => $msg, "data" => $data];
    }

    public function fail($msg = "", $data = []) {
        return ["code" => 0, "msg" => $msg, "data" => $data];
    }
}');

// 包含ParameterSession控制器
require_once __DIR__ . '/../../application/api/controller/ParameterSession.php';

use app\api\controller\ParameterSession;

// 测试函数
function test_parameter_session_api() {
    echo "开始测试ParameterSession API控制器\n";
    echo "====================================\n";
    
    $passed = 0;
    $failed = 0;
    
    // 测试1: 创建会话
    echo "\n--- 测试1: 创建参数填写会话 ---\n";
    try {
        $controller = new ParameterSession([
            'post' => ['goods_id' => 123]
        ]);
        
        $result = $controller->create();
        
        if ($result['code'] == 1) {
            echo "✓ 会话创建成功\n";
            echo "  响应消息: " . $result['msg'] . "\n";
            $passed++;
        } else {
            echo "✗ 会话创建失败: " . $result['msg'] . "\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 测试2: 更新参数
    echo "\n--- 测试2: 更新参数值 ---\n";
    try {
        $controller = new ParameterSession([
            'param' => ['session_id' => 'session_001'],
            'post' => [
                'parameter_values' => [
                    'title' => '我的专属海报',
                    'background_color' => '#ff6b6b'
                ]
            ]
        ]);
        
        $result = $controller->update();
        
        if ($result['code'] == 1) {
            echo "✓ 参数更新成功\n";
            echo "  响应消息: " . $result['msg'] . "\n";
            $passed++;
        } else {
            echo "✗ 参数更新失败: " . $result['msg'] . "\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 测试3: 获取会话详情
    echo "\n--- 测试3: 获取会话详情 ---\n";
    try {
        $controller = new ParameterSession([
            'param' => ['session_id' => 'session_001']
        ]);
        
        $result = $controller->detail();
        
        if ($result['code'] == 1) {
            echo "✓ 获取详情成功\n";
            echo "  响应消息: " . $result['msg'] . "\n";
            $passed++;
        } else {
            echo "✗ 获取详情失败: " . $result['msg'] . "\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 测试4: 获取会话列表
    echo "\n--- 测试4: 获取会话列表 ---\n";
    try {
        $controller = new ParameterSession([
            'get' => ['page' => 1, 'limit' => 10]
        ]);
        
        $result = $controller->lists();
        
        if ($result['code'] == 1) {
            echo "✓ 获取列表成功\n";
            echo "  响应消息: " . $result['msg'] . "\n";
            echo "  数据数量: " . count($result['data']['list']) . "\n";
            $passed++;
        } else {
            echo "✗ 获取列表失败: " . $result['msg'] . "\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 测试5: 参数验证
    echo "\n--- 测试5: 参数验证测试 ---\n";
    try {
        // 测试空商品ID
        $controller = new ParameterSession([
            'post' => ['goods_id' => '']
        ]);
        
        $result = $controller->create();
        
        if ($result['code'] == 0 && strpos($result['msg'], '不能为空') !== false) {
            echo "✓ 参数验证成功\n";
            echo "  错误消息: " . $result['msg'] . "\n";
            $passed++;
        } else {
            echo "✗ 参数验证失败\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 测试6: 方法存在性验证
    echo "\n--- 测试6: 方法存在性验证 ---\n";
    try {
        $reflection = new ReflectionClass('app\api\controller\ParameterSession');
        
        $methods = ['create', 'update', 'detail', 'preview', 'complete', 'lists'];
        $methodCount = 0;
        
        foreach ($methods as $methodName) {
            if ($reflection->hasMethod($methodName) && $reflection->getMethod($methodName)->isPublic()) {
                $methodCount++;
            }
        }
        
        if ($methodCount == count($methods)) {
            echo "✓ 所有API方法都存在\n";
            echo "  方法数量: $methodCount/" . count($methods) . "\n";
            $passed++;
        } else {
            echo "✗ 缺少API方法\n";
            echo "  方法数量: $methodCount/" . count($methods) . "\n";
            $failed++;
        }
    } catch (Exception $e) {
        echo "✗ 反射测试异常: " . $e->getMessage() . "\n";
        $failed++;
    }
    
    // 输出测试结果
    echo "\n====================================\n";
    echo "测试完成\n";
    echo "总计: " . ($passed + $failed) . " 个测试\n";
    echo "通过: $passed 个测试\n";
    echo "失败: $failed 个测试\n";
    
    if ($failed == 0) {
        echo "✓ 所有测试通过！ParameterSession API控制器实现正确。\n";
    } else {
        echo "✗ 有测试失败，需要检查实现。\n";
    }
    
    return $failed == 0;
}

// 执行测试
test_parameter_session_api();

// 清理临时文件
if (file_exists(__DIR__ . '/../../application/api/controller/ApiBase.php')) {
    unlink(__DIR__ . '/../../application/api/controller/ApiBase.php');
}

echo "\n注意：这是ParameterSession API控制器的集成测试，验证了控制器的基本结构和方法。\n";
echo "实际使用时需要配置正确的数据库连接和相关服务。\n";
?>
