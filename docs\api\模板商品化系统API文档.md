# 模板商品化系统API文档

## 概述

模板商品化系统为移动端和第三方集成提供完整的API接口，支持参数填写、订单创建、团购参与、服务交付等核心业务功能。本文档详细描述了所有API接口的使用方法。

## 基础信息

- **基础URL**: `https://api.yoursite.com`
- **API版本**: v1.0.0
- **响应格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

## 通用响应格式

### 成功响应
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### 错误响应
```json
{
  "code": 0,
  "msg": "错误描述",
  "data": {},
  "error": {
    "reason": "详细错误原因",
    "field": "错误字段名（如果适用）"
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 请求失败 | 查看具体错误信息 |
| 1 | 请求成功 | - |
| 401 | 未授权 | 检查token是否有效 |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查请求路径和参数 |
| 422 | 参数验证失败 | 检查请求参数格式 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 认证说明

所有API接口都需要在请求头中携带认证token：

```http
Authorization: Bearer your_access_token
Content-Type: application/json
```

## API接口列表

### 1. 参数填写相关接口

#### 1.1 创建参数填写会话
- **接口**: `POST /api/parameter_session/create`
- **功能**: 为指定商品创建参数填写会话
- **权限**: 需要用户登录

**请求参数**:
```json
{
  "goods_id": 123
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "会话创建成功",
  "data": {
    "session_id": "session_20240115_001",
    "goods_info": {
      "id": 123,
      "name": "海报定制服务",
      "price": 29.90,
      "template_config_id": "template_001"
    },
    "parameter_schema": {
      "title": {
        "type": "text",
        "label": "标题",
        "required": true,
        "max_length": 50,
        "placeholder": "请输入海报标题"
      },
      "subtitle": {
        "type": "text",
        "label": "副标题",
        "required": false,
        "max_length": 100,
        "placeholder": "请输入副标题（可选）"
      },
      "background_color": {
        "type": "color",
        "label": "背景颜色",
        "required": false,
        "default": "#ffffff"
      }
    },
    "expires_at": "2024-01-16T10:00:00Z",
    "created_at": "2024-01-15T10:00:00Z"
  }
}
```

#### 1.2 更新参数值
- **接口**: `PUT /api/parameter_session/{session_id}`
- **功能**: 更新参数填写会话的参数值
- **权限**: 需要用户登录且为会话所有者

**请求参数**:
```json
{
  "parameter_values": {
    "title": "我的专属海报",
    "subtitle": "专业定制服务",
    "background_color": "#ff6b6b"
  }
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "参数更新成功",
  "data": {
    "session_id": "session_20240115_001",
    "completion_rate": 85,
    "can_preview": true,
    "can_submit": false,
    "validation_errors": [],
    "updated_at": "2024-01-15T10:05:00Z"
  }
}
```

#### 1.3 获取会话详情
- **接口**: `GET /api/parameter_session/{session_id}`
- **功能**: 获取参数填写会话的详细信息
- **权限**: 需要用户登录且为会话所有者

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "session_id": "session_20240115_001",
    "goods_info": {
      "id": 123,
      "name": "海报定制服务",
      "price": 29.90
    },
    "parameter_values": {
      "title": "我的专属海报",
      "subtitle": "专业定制服务",
      "background_color": "#ff6b6b"
    },
    "completion_rate": 85,
    "is_completed": false,
    "expires_at": "2024-01-16T10:00:00Z",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:05:00Z"
  }
}
```

#### 1.4 生成预览
- **接口**: `POST /api/parameter_session/{session_id}/preview`
- **功能**: 根据当前参数值生成预览图
- **权限**: 需要用户登录且为会话所有者

**请求参数**:
```json
{
  "preview_options": {
    "width": 800,
    "height": 600,
    "quality": 0.8
  }
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "预览生成成功",
  "data": {
    "preview_url": "https://cdn.yoursite.com/previews/session_20240115_001.jpg",
    "preview_expires_at": "2024-01-16T10:00:00Z",
    "generation_time": 2.5,
    "file_size": 245760
  }
}
```

#### 1.5 完成填写
- **接口**: `POST /api/parameter_session/{session_id}/complete`
- **功能**: 完成参数填写，准备创建订单
- **权限**: 需要用户登录且为会话所有者

**响应示例**:
```json
{
  "code": 1,
  "msg": "参数填写完成",
  "data": {
    "session_id": "session_20240115_001",
    "is_completed": true,
    "completion_rate": 100,
    "can_create_order": true,
    "completed_at": "2024-01-15T10:10:00Z"
  }
}
```

### 2. 服务订单相关接口

#### 2.1 创建服务订单
- **接口**: `POST /api/service_order/create`
- **功能**: 基于完成的参数填写会话创建服务订单
- **权限**: 需要用户登录

**请求参数**:
```json
{
  "session_id": "session_20240115_001",
  "address_id": 123,
  "print_options": {
    "material": "premium_paper",
    "size": "A4",
    "quantity": 1
  },
  "payment_method": "wechat",
  "remark": "请尽快处理"
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "订单创建成功",
  "data": {
    "order_id": 10001,
    "order_sn": "SO202401150001",
    "total_amount": 29.90,
    "goods_info": {
      "name": "海报定制服务",
      "price": 29.90
    },
    "service_info": {
      "material": "premium_paper",
      "size": "A4",
      "quantity": 1,
      "estimated_delivery": "2024-01-20"
    },
    "payment_info": {
      "payment_method": "wechat",
      "payment_url": "weixin://wxpay/bizpayurl?pr=abc123",
      "expires_at": "2024-01-15T10:30:00Z"
    },
    "created_at": "2024-01-15T10:15:00Z"
  }
}
```

#### 2.2 获取订单状态
- **接口**: `GET /api/service_order/{order_id}/status`
- **功能**: 获取服务订单的当前状态
- **权限**: 需要用户登录且为订单所有者

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "order_id": 10001,
    "order_sn": "SO202401150001",
    "order_status": "paid",
    "order_status_text": "已支付",
    "pay_status": "paid",
    "pay_status_text": "已支付",
    "delivery_status": "generating",
    "delivery_status_text": "生成中",
    "progress": {
      "current_step": 2,
      "total_steps": 5,
      "steps": [
        {"name": "订单确认", "status": "completed", "time": "2024-01-15T10:15:00Z"},
        {"name": "支付完成", "status": "completed", "time": "2024-01-15T10:20:00Z"},
        {"name": "海报生成", "status": "processing", "time": null},
        {"name": "打印制作", "status": "pending", "time": null},
        {"name": "物流配送", "status": "pending", "time": null}
      ]
    },
    "estimated_delivery": "2024-01-20",
    "updated_at": "2024-01-15T10:20:00Z"
  }
}
```

#### 2.3 获取交付信息
- **接口**: `GET /api/service_order/{order_id}/delivery`
- **功能**: 获取服务订单的交付详细信息
- **权限**: 需要用户登录且为订单所有者

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "order_id": 10001,
    "delivery_status": "shipped",
    "delivery_status_text": "已发货",
    "poster_info": {
      "download_url": "https://cdn.yoursite.com/posters/SO202401150001.jpg",
      "file_size": 2048000,
      "resolution": "3508x2480",
      "generated_at": "2024-01-15T12:00:00Z"
    },
    "print_info": {
      "material": "premium_paper",
      "size": "A4",
      "quantity": 1,
      "printed_at": "2024-01-16T09:00:00Z"
    },
    "shipping_info": {
      "express_company": "顺丰速运",
      "tracking_number": "SF1234567890",
      "shipped_at": "2024-01-16T14:00:00Z",
      "estimated_arrival": "2024-01-18T18:00:00Z"
    },
    "address_info": {
      "recipient": "张三",
      "mobile": "13800138000",
      "address": "北京市朝阳区xxx街道xxx号"
    }
  }
}
```

#### 2.4 下载生成文件
- **接口**: `POST /api/service_order/{order_id}/download`
- **功能**: 获取生成文件的下载链接
- **权限**: 需要用户登录且为订单所有者

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "download_url": "https://cdn.yoursite.com/downloads/SO202401150001.jpg?token=abc123",
    "expires_at": "2024-01-15T18:00:00Z",
    "file_info": {
      "filename": "我的专属海报.jpg",
      "file_size": 2048000,
      "format": "JPEG",
      "resolution": "3508x2480"
    }
  }
}
```

#### 2.5 获取用户订单列表
- **接口**: `GET /api/service_order/lists`
- **功能**: 获取当前用户的服务订单列表
- **权限**: 需要用户登录

**请求参数**:
```
?page=1&limit=10&status=all&sort=create_time_desc
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "order_id": 10001,
        "order_sn": "SO202401150001",
        "goods_name": "海报定制服务",
        "total_amount": 29.90,
        "order_status": "paid",
        "order_status_text": "已支付",
        "delivery_status": "generating",
        "delivery_status_text": "生成中",
        "created_at": "2024-01-15T10:15:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

#### 2.6 取消订单
- **接口**: `POST /api/service_order/{order_id}/cancel`
- **功能**: 取消未支付的服务订单
- **权限**: 需要用户登录且为订单所有者

**请求参数**:
```json
{
  "cancel_reason": "不需要了"
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "订单取消成功",
  "data": {
    "order_id": 10001,
    "order_status": "cancelled",
    "cancelled_at": "2024-01-15T10:25:00Z"
  }
}
```

### 3. 团购相关接口

#### 3.1 获取团购活动列表
- **接口**: `GET /api/group_buy/lists`
- **功能**: 获取可参与的团购活动列表
- **权限**: 需要用户登录

**请求参数**:
```
?page=1&limit=10&status=active&sort=create_time_desc&keyword=海报
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "activity_id": "GB20240115001",
        "goods_info": {
          "id": 123,
          "name": "海报定制服务",
          "image": "https://cdn.yoursite.com/goods/123.jpg",
          "original_price": 29.90
        },
        "group_price": 19.90,
        "min_participants": 3,
        "current_participants": 2,
        "max_participants": 10,
        "status": "active",
        "status_text": "进行中",
        "start_time": "2024-01-15T00:00:00Z",
        "end_time": "2024-01-16T23:59:59Z",
        "remaining_time": 86400,
        "success_rate": 85,
        "created_at": "2024-01-15T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

#### 3.2 获取团购活动详情
- **接口**: `GET /api/group_buy/{activity_id}`
- **功能**: 获取团购活动的详细信息
- **权限**: 需要用户登录

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "activity_id": "GB20240115001",
    "goods_info": {
      "id": 123,
      "name": "海报定制服务",
      "description": "专业海报定制服务，支持个性化参数设置",
      "image": "https://cdn.yoursite.com/goods/123.jpg",
      "original_price": 29.90,
      "template_config_id": "template_001"
    },
    "group_info": {
      "group_price": 19.90,
      "discount_amount": 10.00,
      "discount_rate": 33,
      "min_participants": 3,
      "current_participants": 2,
      "max_participants": 10,
      "remaining_slots": 8
    },
    "time_info": {
      "start_time": "2024-01-15T00:00:00Z",
      "end_time": "2024-01-16T23:59:59Z",
      "remaining_time": 86400,
      "status": "active",
      "status_text": "进行中"
    },
    "participants": [
      {
        "user_id": 1001,
        "nickname": "用户A",
        "avatar": "https://cdn.yoursite.com/avatars/1001.jpg",
        "joined_at": "2024-01-15T08:00:00Z"
      },
      {
        "user_id": 1002,
        "nickname": "用户B",
        "avatar": "https://cdn.yoursite.com/avatars/1002.jpg",
        "joined_at": "2024-01-15T09:00:00Z"
      }
    ],
    "rules": {
      "description": "团购规则说明",
      "success_condition": "达到最少参与人数即可成团",
      "refund_policy": "未成团自动退款"
    }
  }
}
```

#### 3.3 参与团购
- **接口**: `POST /api/group_buy/{activity_id}/join`
- **功能**: 参与指定的团购活动
- **权限**: 需要用户登录

**请求参数**:
```json
{
  "session_id": "session_20240115_001",
  "address_id": 123,
  "print_options": {
    "material": "premium_paper",
    "size": "A4",
    "quantity": 1
  },
  "payment_method": "wechat"
}
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "参与团购成功",
  "data": {
    "participant_id": "GB20240115001_1003",
    "activity_id": "GB20240115001",
    "order_info": {
      "order_id": 10002,
      "order_sn": "SO202401150002",
      "total_amount": 19.90,
      "payment_info": {
        "payment_method": "wechat",
        "payment_url": "weixin://wxpay/bizpayurl?pr=def456",
        "expires_at": "2024-01-15T11:00:00Z"
      }
    },
    "group_status": {
      "current_participants": 3,
      "min_participants": 3,
      "is_grouped": true,
      "grouped_at": "2024-01-15T10:30:00Z"
    },
    "joined_at": "2024-01-15T10:30:00Z"
  }
}
```

#### 3.4 获取用户团购记录
- **接口**: `GET /api/group_buy/my_records`
- **功能**: 获取当前用户的团购参与记录
- **权限**: 需要用户登录

**请求参数**:
```
?page=1&limit=10&status=all&sort=create_time_desc
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "participant_id": "GB20240115001_1003",
        "activity_id": "GB20240115001",
        "goods_name": "海报定制服务",
        "group_price": 19.90,
        "original_price": 29.90,
        "order_sn": "SO202401150002",
        "group_status": "grouped",
        "group_status_text": "已成团",
        "order_status": "paid",
        "order_status_text": "已支付",
        "joined_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 4. 商品相关接口

#### 4.1 获取服务商品列表
- **接口**: `GET /api/goods/service_list`
- **功能**: 获取支持模板定制的服务商品列表
- **权限**: 需要用户登录

**请求参数**:
```
?page=1&limit=20&category_id=1&keyword=海报&sort=create_time_desc
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "goods_id": 123,
        "name": "海报定制服务",
        "description": "专业海报定制服务，支持个性化参数设置",
        "image": "https://cdn.yoursite.com/goods/123.jpg",
        "price": 29.90,
        "market_price": 39.90,
        "goods_type": 2,
        "service_config": {
          "template_config_id": "template_001",
          "support_materials": ["premium_paper", "canvas"],
          "support_sizes": ["A4", "A3", "A2"],
          "delivery_time": "3-5个工作日"
        },
        "sales_count": 1250,
        "rating": 4.8,
        "status": 1,
        "created_at": "2024-01-10T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 15,
      "total_pages": 1
    }
  }
}
```

#### 4.2 获取商品详情
- **接口**: `GET /api/goods/{goods_id}`
- **功能**: 获取服务商品的详细信息
- **权限**: 需要用户登录

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "goods_id": 123,
    "name": "海报定制服务",
    "description": "专业海报定制服务，支持个性化参数设置，高质量打印，快速配送",
    "image": "https://cdn.yoursite.com/goods/123.jpg",
    "images": [
      "https://cdn.yoursite.com/goods/123_1.jpg",
      "https://cdn.yoursite.com/goods/123_2.jpg"
    ],
    "price": 29.90,
    "market_price": 39.90,
    "goods_type": 2,
    "service_config": {
      "template_config_id": "template_001",
      "template_name": "商务海报模板",
      "parameter_schema": {
        "title": {
          "type": "text",
          "label": "标题",
          "required": true,
          "max_length": 50
        },
        "subtitle": {
          "type": "text",
          "label": "副标题",
          "required": false,
          "max_length": 100
        }
      },
      "support_materials": [
        {"value": "premium_paper", "label": "高级纸张", "price_add": 0},
        {"value": "canvas", "label": "画布", "price_add": 10.00}
      ],
      "support_sizes": [
        {"value": "A4", "label": "A4 (21x29.7cm)", "price_add": 0},
        {"value": "A3", "label": "A3 (29.7x42cm)", "price_add": 15.00}
      ],
      "delivery_time": "3-5个工作日"
    },
    "sales_count": 1250,
    "rating": 4.8,
    "reviews_count": 156,
    "status": 1,
    "created_at": "2024-01-10T00:00:00Z"
  }
}
```

### 5. 用户相关接口

#### 5.1 获取用户信息
- **接口**: `GET /api/user/info`
- **功能**: 获取当前登录用户的基本信息
- **权限**: 需要用户登录

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "user_id": 1003,
    "nickname": "张三",
    "avatar": "https://cdn.yoursite.com/avatars/1003.jpg",
    "mobile": "138****8000",
    "email": "<EMAIL>",
    "level": "VIP",
    "balance": 100.50,
    "points": 2580,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 5.2 获取用户地址列表
- **接口**: `GET /api/user/address`
- **功能**: 获取用户的收货地址列表
- **权限**: 需要用户登录

**响应示例**:
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "address_id": 123,
        "recipient": "张三",
        "mobile": "13800138000",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "xxx街道xxx号",
        "full_address": "北京市北京市朝阳区xxx街道xxx号",
        "is_default": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 6. 文件上传接口

#### 6.1 上传图片
- **接口**: `POST /api/upload/image`
- **功能**: 上传图片文件（用于自定义参数）
- **权限**: 需要用户登录

**请求参数**:
```
Content-Type: multipart/form-data

file: [图片文件]
type: avatar|goods|parameter
```

**响应示例**:
```json
{
  "code": 1,
  "msg": "上传成功",
  "data": {
    "file_url": "https://cdn.yoursite.com/uploads/20240115/abc123.jpg",
    "file_name": "abc123.jpg",
    "file_size": 245760,
    "file_type": "image/jpeg",
    "width": 800,
    "height": 600,
    "uploaded_at": "2024-01-15T10:00:00Z"
  }
}
```

## SDK使用示例

### JavaScript/UniApp示例

```javascript
// API基础配置
const API_BASE_URL = 'https://api.yoursite.com';
const token = 'your_access_token';

// 通用请求方法
function apiRequest(method, url, data = {}) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: API_BASE_URL + url,
      method: method,
      data: data,
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 1) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 使用示例：创建参数填写会话
async function createParameterSession(goodsId) {
  try {
    const result = await apiRequest('POST', '/api/parameter_session/create', {
      goods_id: goodsId
    });
    console.log('会话创建成功:', result);
    return result;
  } catch (error) {
    console.error('会话创建失败:', error.message);
    throw error;
  }
}

// 使用示例：更新参数值
async function updateParameters(sessionId, parameters) {
  try {
    const result = await apiRequest('PUT', `/api/parameter_session/${sessionId}`, {
      parameter_values: parameters
    });
    console.log('参数更新成功:', result);
    return result;
  } catch (error) {
    console.error('参数更新失败:', error.message);
    throw error;
  }
}

// 使用示例：创建服务订单
async function createServiceOrder(sessionId, addressId, printOptions) {
  try {
    const result = await apiRequest('POST', '/api/service_order/create', {
      session_id: sessionId,
      address_id: addressId,
      print_options: printOptions,
      payment_method: 'wechat'
    });
    console.log('订单创建成功:', result);
    return result;
  } catch (error) {
    console.error('订单创建失败:', error.message);
    throw error;
  }
}
```

### PHP示例

```php
<?php
class TemplateGoodsAPI {
    private $baseUrl;
    private $token;

    public function __construct($baseUrl, $token) {
        $this->baseUrl = $baseUrl;
        $this->token = $token;
    }

    private function request($method, $url, $data = []) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->token,
                'Content-Type: application/json'
            ]
        ]);

        if (!empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $result = json_decode($response, true);

        if ($httpCode !== 200 || $result['code'] !== 1) {
            throw new Exception($result['msg'] ?? 'API请求失败');
        }

        return $result['data'];
    }

    // 创建参数填写会话
    public function createParameterSession($goodsId) {
        return $this->request('POST', '/api/parameter_session/create', [
            'goods_id' => $goodsId
        ]);
    }

    // 更新参数值
    public function updateParameters($sessionId, $parameters) {
        return $this->request('PUT', "/api/parameter_session/{$sessionId}", [
            'parameter_values' => $parameters
        ]);
    }

    // 创建服务订单
    public function createServiceOrder($sessionId, $addressId, $printOptions) {
        return $this->request('POST', '/api/service_order/create', [
            'session_id' => $sessionId,
            'address_id' => $addressId,
            'print_options' => $printOptions,
            'payment_method' => 'wechat'
        ]);
    }
}

// 使用示例
try {
    $api = new TemplateGoodsAPI('https://api.yoursite.com', 'your_access_token');

    // 创建会话
    $session = $api->createParameterSession(123);
    echo "会话创建成功: " . $session['session_id'] . "\n";

    // 更新参数
    $result = $api->updateParameters($session['session_id'], [
        'title' => '我的海报',
        'subtitle' => '个性化定制'
    ]);
    echo "参数更新成功，完成度: " . $result['completion_rate'] . "%\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
```

## 最佳实践

### 1. 错误处理
- 始终检查响应中的 `code` 字段
- 根据不同的错误码进行相应的处理
- 对网络错误进行重试机制
- 记录错误日志便于调试

### 2. 性能优化
- 使用分页获取列表数据，避免一次性加载过多数据
- 合理使用缓存，减少重复请求
- 对图片等资源进行压缩和CDN加速
- 使用连接池复用HTTP连接

### 3. 安全建议
- 妥善保管访问token，避免泄露
- 使用HTTPS协议进行通信
- 对敏感参数进行加密传输
- 实施请求频率限制

### 4. 业务流程建议
- 参数填写会话有过期时间，请及时完成填写
- 订单创建后请及时支付，避免超时取消
- 定期检查订单状态，及时处理异常情况
- 合理使用预览功能，确认效果后再下单

## 常见问题

### Q1: 如何处理token过期？
A: 当收到401错误码时，说明token已过期，需要重新获取token。

### Q2: 参数填写会话什么时候会过期？
A: 会话默认24小时后过期，过期后需要重新创建会话。

### Q3: 如何获取订单的实时状态？
A: 可以通过轮询 `/api/service_order/{order_id}/status` 接口获取最新状态。

### Q4: 团购未成团怎么办？
A: 系统会自动处理未成团的情况，相关费用会原路退回。

### Q5: 如何处理文件上传失败？
A: 检查文件大小和格式是否符合要求，必要时进行文件压缩。

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持参数填写、订单创建、团购参与等核心功能
- 提供完整的API接口文档和示例代码

---

**文档维护**: 开发团队
**最后更新**: 2024-01-15
**技术支持**: <EMAIL>
