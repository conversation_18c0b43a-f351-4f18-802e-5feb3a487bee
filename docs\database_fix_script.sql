-- =====================================================
-- 模板商品化系统数据库结构修复脚本
-- 修复验证脚本发现的问题
-- 执行前请备份数据库！
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 修复1: ls_template_order_params 表添加 session_id 字段
-- =====================================================

-- 检查字段是否已存在
SELECT '正在检查 ls_template_order_params.session_id 字段...' as message;

SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_template_order_params` ADD COLUMN `session_id` VARCHAR(32) NULL COMMENT ''参数填写会话ID'' AFTER `order_id`, ADD INDEX `idx_session_id` (`session_id`);',
        'SELECT ''字段 session_id 已存在，跳过添加'' as message;'
    )
    FROM information_schema.COLUMNS 
    WHERE table_schema = DATABASE() 
    AND table_name = 'ls_template_order_params' 
    AND COLUMN_NAME = 'session_id'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 修复2: ls_file_download_logs 表添加 file_path 字段
-- =====================================================

-- 检查字段是否已存在
SELECT '正在检查 ls_file_download_logs.file_path 字段...' as message;

SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_file_download_logs` ADD COLUMN `file_path` VARCHAR(500) NOT NULL COMMENT ''文件路径'' AFTER `user_id`;',
        'SELECT ''字段 file_path 已存在，跳过添加'' as message;'
    )
    FROM information_schema.COLUMNS 
    WHERE table_schema = DATABASE() 
    AND table_name = 'ls_file_download_logs' 
    AND COLUMN_NAME = 'file_path'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 修复3: 添加团购参与记录表外键约束
-- =====================================================

-- 检查外键约束是否已存在
SELECT '正在检查团购参与记录表外键约束...' as message;

SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_group_buy_participants` ADD CONSTRAINT `fk_participants_activity` FOREIGN KEY (`activity_id`) REFERENCES `ls_group_buy_activities`(`id`) ON DELETE CASCADE;',
        'SELECT ''外键约束 fk_participants_activity 已存在，跳过添加'' as message;'
    )
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE table_schema = DATABASE() 
    AND table_name = 'ls_group_buy_participants' 
    AND CONSTRAINT_NAME = 'fk_participants_activity'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 修复4: 添加模板订单参数表外键约束
-- =====================================================

-- 检查外键约束是否已存在
SELECT '正在检查模板订单参数表外键约束...' as message;

SET @sql = (
    SELECT IF(
        COUNT(*) = 0,
        'ALTER TABLE `ls_template_order_params` ADD CONSTRAINT `fk_params_order` FOREIGN KEY (`order_id`) REFERENCES `ls_order`(`id`) ON DELETE CASCADE;',
        'SELECT ''外键约束 fk_params_order 已存在，跳过添加'' as message;'
    )
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE table_schema = DATABASE() 
    AND table_name = 'ls_template_order_params' 
    AND CONSTRAINT_NAME = 'fk_params_order'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 验证修复结果
-- =====================================================

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

SELECT '=== 修复完成，验证结果 ===' as message;

-- 验证字段添加
SELECT 
    '字段验证' as category,
    CASE 
        WHEN COUNT(CASE WHEN table_name = 'ls_template_order_params' AND COLUMN_NAME = 'session_id' THEN 1 END) > 0 
        AND COUNT(CASE WHEN table_name = 'ls_file_download_logs' AND COLUMN_NAME = 'file_path' THEN 1 END) > 0
        THEN '✓ 所有缺失字段已添加'
        ELSE '✗ 仍有字段缺失'
    END as result
FROM information_schema.COLUMNS 
WHERE table_schema = DATABASE() 
AND ((table_name = 'ls_template_order_params' AND COLUMN_NAME = 'session_id')
     OR (table_name = 'ls_file_download_logs' AND COLUMN_NAME = 'file_path'));

-- 验证外键约束
SELECT 
    '外键约束验证' as category,
    CASE 
        WHEN COUNT(CASE WHEN CONSTRAINT_NAME = 'fk_participants_activity' THEN 1 END) > 0 
        AND COUNT(CASE WHEN CONSTRAINT_NAME = 'fk_params_order' THEN 1 END) > 0
        THEN '✓ 所有外键约束已添加'
        ELSE '✗ 仍有外键约束缺失'
    END as result
FROM information_schema.KEY_COLUMN_USAGE 
WHERE table_schema = DATABASE() 
AND CONSTRAINT_NAME IN ('fk_participants_activity', 'fk_params_order');

SELECT '=== 修复脚本执行完成 ===' as message;
SELECT '请重新运行验证脚本确认修复结果' as suggestion;
