# 模板商品化系统业务流程测试用例

## 测试目标
对整个模板商品化系统进行端到端的业务流程测试，确保所有功能模块能够正确协同工作，验证单人购买流程和团购流程的完整性和稳定性。

## 测试环境要求
- 完整的模板商品化系统已部署
- 数据库扩展完成，所有表结构正确
- 海报生成服务正常运行
- 支付系统正常工作
- 物流系统接口可用
- 测试数据已准备完成

## 测试数据准备

### 基础测试数据
- **管理员账号**: admin / admin123
- **测试用户**: user001 / 123456, user002 / 123456, user003 / 123456
- **测试商品**: 海报定制服务（ID: 1001）
- **模板配置**: template_001（商务海报模板）
- **收货地址**: 北京市朝阳区xxx街道xxx号

### 模板参数配置
```json
{
  "title": {"type": "text", "required": true, "max_length": 50},
  "subtitle": {"type": "text", "required": false, "max_length": 100},
  "background_color": {"type": "color", "required": false, "default": "#ffffff"},
  "logo_image": {"type": "image", "required": false, "max_size": "2MB"}
}
```

## 业务流程测试用例

### BF001: 单人购买完整流程测试
**测试目的**: 验证用户从浏览商品到收到实物海报的完整购买流程
**测试类型**: 端到端业务流程测试
**预计执行时间**: 30分钟

**测试步骤**:

#### 第一阶段：商品浏览和选择
1. **用户登录系统**
   - 使用测试用户user001登录移动端
   - 验证登录成功，获取用户token

2. **浏览模板商品**
   - 访问商品列表页面
   - 筛选服务商品类型
   - 查看海报定制服务详情

3. **查看商品详情**
   - 验证商品信息显示完整（名称、价格、描述、预览图）
   - 验证模板参数说明清晰
   - 验证服务条款和交付说明

**预期结果**:
- 用户能够正常浏览和查看服务商品
- 商品信息显示完整准确
- 模板参数说明清晰易懂

#### 第二阶段：参数填写和预览
4. **创建参数填写会话**
   - 点击"开始定制"按钮
   - 验证参数填写会话创建成功
   - 获取会话ID和过期时间

5. **填写模板参数**
   - 填写标题："我的专属海报"
   - 填写副标题："专业定制服务"
   - 选择背景颜色：#ff6b6b
   - 上传logo图片（可选）

6. **参数验证和保存**
   - 验证必填参数检查正确
   - 验证参数格式验证正确
   - 保存参数并验证完成度计算

7. **生成预览**
   - 请求生成预览图片
   - 验证预览生成成功
   - 验证预览图片质量和效果

**预期结果**:
- 参数填写界面友好易用
- 参数验证逻辑正确
- 预览生成功能正常
- 预览效果符合预期

#### 第三阶段：订单创建和支付
8. **确认订单信息**
   - 验证参数信息显示正确
   - 选择打印材质和尺寸
   - 选择收货地址

9. **创建服务订单**
   - 提交订单创建请求
   - 验证订单创建成功
   - 获取订单号和支付信息

10. **完成支付流程**
    - 选择支付方式（微信支付）
    - 模拟支付成功回调
    - 验证订单状态更新为已支付

**预期结果**:
- 订单创建流程顺畅
- 支付信息生成正确
- 支付成功后状态更新及时

#### 第四阶段：服务交付流程
11. **海报生成阶段**
    - 验证支付成功后自动触发海报生成
    - 验证生成任务创建成功
    - 验证海报生成完成并保存

12. **打印制作阶段**
    - 管理员标记打印开始
    - 验证打印状态更新
    - 管理员标记打印完成

13. **物流发货阶段**
    - 管理员录入物流信息
    - 验证物流跟踪号生成
    - 验证发货状态更新

14. **用户跟踪和确认**
    - 用户查询订单状态
    - 验证交付进度显示正确
    - 验证物流信息实时更新

**预期结果**:
- 海报生成流程自动化
- 打印和物流管理便捷
- 用户能够实时跟踪进度
- 整个交付流程透明可控

### BF002: 团购流程完整测试
**测试目的**: 验证团购活动从创建到成团交付的完整流程
**测试类型**: 端到端业务流程测试
**预计执行时间**: 45分钟

**测试步骤**:

#### 第一阶段：团购活动创建
1. **管理员创建团购活动**
   - 管理员登录后台系统
   - 选择支持团购的商品
   - 设置团购参数：最小人数3人，团购价格19.90元
   - 设置活动时长：24小时
   - 发布团购活动

2. **验证团购活动发布**
   - 验证团购活动在前端显示
   - 验证团购信息显示正确
   - 验证团购状态为"进行中"

**预期结果**:
- 团购活动创建成功
- 团购信息显示完整
- 活动状态管理正确

#### 第二阶段：用户参与团购
3. **第一个用户参与团购**
   - 用户user001浏览团购商品
   - 填写个性化参数（标题："用户1的海报"）
   - 支付团购价格参与团购
   - 验证参与成功，当前人数1/3

4. **第二个用户参与团购**
   - 用户user002参与同一团购
   - 填写不同的个性化参数（标题："用户2的海报"）
   - 完成支付流程
   - 验证当前人数2/3

5. **第三个用户参与团购（成团）**
   - 用户user003参与团购
   - 填写个性化参数（标题："用户3的海报"）
   - 完成支付后自动成团
   - 验证团购状态变为"已成团"

**预期结果**:
- 用户能够正常参与团购
- 每个用户的参数独立管理
- 成团逻辑正确触发
- 团购进度实时更新

#### 第三阶段：团购成团后处理
6. **批量海报生成**
   - 验证成团后自动触发批量生成
   - 验证为每个参与者生成独立海报
   - 验证生成任务状态管理

7. **批量打印管理**
   - 管理员查看团购订单列表
   - 批量标记打印开始
   - 批量标记打印完成

8. **批量物流处理**
   - 管理员批量录入物流信息
   - 验证每个用户的物流跟踪
   - 验证发货状态批量更新

**预期结果**:
- 批量处理功能高效
- 每个用户的订单独立管理
- 物流信息准确分发

### BF003: 团购失败流程测试
**测试目的**: 验证团购活动未达到成团条件时的失败处理流程
**测试类型**: 异常流程测试
**预计执行时间**: 20分钟

**测试步骤**:

1. **创建团购活动**
   - 设置最小成团人数5人
   - 设置活动时长1小时（便于测试）

2. **部分用户参与**
   - 2个用户参与团购
   - 验证团购进度2/5

3. **等待活动过期**
   - 等待活动时间结束
   - 验证系统自动检测活动过期

4. **自动失败处理**
   - 验证团购状态变为"已失败"
   - 验证自动退款流程启动
   - 验证用户收到失败通知

**预期结果**:
- 团购失败检测准确
- 自动退款流程正确
- 用户通知及时到达

### BF004: 异常情况处理测试
**测试目的**: 验证系统在各种异常情况下的处理能力
**测试类型**: 异常处理测试
**预计执行时间**: 30分钟

**测试场景**:

#### 场景1：参数填写会话过期
1. 创建参数填写会话
2. 等待会话过期（或手动设置过期）
3. 尝试继续填写参数
4. 验证过期提示和重新创建引导

#### 场景2：海报生成失败
1. 模拟海报生成API异常
2. 验证生成失败检测
3. 验证自动重试机制
4. 验证失败通知和人工处理

#### 场景3：支付异常处理
1. 模拟支付回调异常
2. 验证订单状态一致性
3. 验证支付状态同步机制
4. 验证异常订单处理

#### 场景4：库存不足处理
1. 设置商品库存为0
2. 尝试创建订单
3. 验证库存检查逻辑
4. 验证用户友好提示

**预期结果**:
- 异常情况得到正确处理
- 用户体验不受严重影响
- 数据一致性得到保证
- 异常恢复机制有效

### BF005: 性能和并发测试
**测试目的**: 验证系统在高并发情况下的稳定性和性能
**测试类型**: 性能测试
**预计执行时间**: 60分钟

**测试场景**:

#### 场景1：并发参数填写
1. 模拟100个用户同时创建参数填写会话
2. 验证会话创建成功率
3. 验证数据库连接池稳定性
4. 验证响应时间在可接受范围内

#### 场景2：并发团购参与
1. 创建热门团购活动
2. 模拟50个用户同时参与
3. 验证成团逻辑的并发安全性
4. 验证支付处理的并发能力

#### 场景3：批量海报生成
1. 创建包含100个订单的批量生成任务
2. 验证生成任务队列处理能力
3. 验证系统资源使用情况
4. 验证生成完成率和错误率

**预期结果**:
- 系统在高并发下保持稳定
- 响应时间在可接受范围内
- 数据一致性得到保证
- 错误率控制在合理范围内

## 测试执行计划

### 测试阶段安排
1. **第1天**: 执行BF001单人购买流程测试
2. **第2天**: 执行BF002团购流程测试
3. **第3天**: 执行BF003团购失败流程测试
4. **第4天**: 执行BF004异常情况处理测试
5. **第5天**: 执行BF005性能和并发测试

### 测试环境配置
- **测试环境**: 独立的测试服务器
- **数据库**: 测试专用数据库，包含完整测试数据
- **外部服务**: 海报生成服务、支付服务的测试环境
- **监控工具**: 性能监控、日志监控、错误监控

### 测试通过标准
- **功能完整性**: 所有核心业务流程正常运行
- **数据一致性**: 所有数据操作保持一致性
- **用户体验**: 界面友好，操作流畅，错误提示清晰
- **性能指标**: API响应时间<2秒，页面加载时间<3秒
- **稳定性**: 连续运行24小时无严重错误
- **并发能力**: 支持100并发用户正常使用

### 缺陷分级标准
- **严重缺陷**: 影响核心业务流程，导致系统不可用
- **重要缺陷**: 影响主要功能，但有替代方案
- **一般缺陷**: 影响用户体验，但不影响核心功能
- **轻微缺陷**: 界面显示问题，不影响功能使用

## 测试报告要求
- **测试执行情况**: 详细记录每个测试用例的执行结果
- **缺陷统计**: 按严重程度统计发现的缺陷
- **性能数据**: 记录关键性能指标
- **改进建议**: 提出系统优化和改进建议
- **风险评估**: 评估系统上线风险和应对措施
