#!/bin/bash

# 模板商品化系统数据库结构验证脚本
# 使用方法: bash database_validation.sh
# 
# 配置数据库连接信息
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="likeshop"
DB_USER="root"
DB_PASS=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 统计变量
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 执行SQL查询
execute_sql() {
    local sql="$1"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$sql" 2>/dev/null
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    if execute_sql "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
}

# 验证商品表扩展字段
validate_goods_table_extension() {
    log_info "验证商品表扩展字段..."
    
    local expected_fields=(
        "goods_type"
        "template_config_id"
        "service_delivery_time"
        "print_materials"
        "print_sizes"
        "support_group_buy"
        "group_buy_min_count"
        "group_buy_price"
        "group_buy_duration"
    )
    
    for field in "${expected_fields[@]}"; do
        local result=$(execute_sql "DESCRIBE ls_goods $field;" 2>/dev/null | wc -l)
        if [ "$result" -gt 0 ]; then
            log_success "商品表字段 $field 存在"
        else
            log_error "商品表字段 $field 不存在"
        fi
    done
    
    # 检查索引
    local indexes=$(execute_sql "SHOW INDEX FROM ls_goods WHERE Key_name IN ('idx_goods_type', 'idx_template_config');" | wc -l)
    if [ "$indexes" -gt 2 ]; then
        log_success "商品表索引创建正常"
    else
        log_error "商品表索引缺失"
    fi
}

# 验证新核心表
validate_new_tables() {
    log_info "验证新核心表创建..."
    
    local expected_tables=(
        "ls_parameter_fill_sessions"
        "ls_template_order_params"
        "ls_service_delivery_records"
        "ls_group_buy_activities"
        "ls_group_buy_participants"
        "ls_file_download_logs"
    )
    
    for table in "${expected_tables[@]}"; do
        local result=$(execute_sql "SHOW TABLES LIKE '$table';" | wc -l)
        if [ "$result" -gt 0 ]; then
            log_success "表 $table 存在"
            
            # 验证表结构
            validate_table_structure "$table"
        else
            log_error "表 $table 不存在"
        fi
    done
}

# 验证表结构
validate_table_structure() {
    local table_name="$1"
    
    case "$table_name" in
        "ls_parameter_fill_sessions")
            local required_fields=("id" "user_id" "goods_id" "template_config_id" "parameter_values" "is_completed")
            ;;
        "ls_template_order_params")
            local required_fields=("id" "order_id" "session_id" "parameter_values")
            ;;
        "ls_service_delivery_records")
            local required_fields=("id" "order_id" "order_goods_id" "delivery_status")
            ;;
        "ls_group_buy_activities")
            local required_fields=("id" "goods_id" "admin_user_id" "activity_title" "min_participants")
            ;;
        "ls_group_buy_participants")
            local required_fields=("id" "activity_id" "user_id" "session_id")
            ;;
        "ls_file_download_logs")
            local required_fields=("id" "user_id" "file_path" "download_time")
            ;;
        *)
            return
            ;;
    esac
    
    local missing_fields=()
    for field in "${required_fields[@]}"; do
        local result=$(execute_sql "DESCRIBE $table_name $field;" 2>/dev/null | wc -l)
        if [ "$result" -eq 0 ]; then
            missing_fields+=("$field")
        fi
    done
    
    if [ ${#missing_fields[@]} -eq 0 ]; then
        log_success "表 $table_name 结构完整"
    else
        log_error "表 $table_name 缺少字段: ${missing_fields[*]}"
    fi
}

# 验证数据完整性
validate_data_integrity() {
    log_info "验证数据完整性..."
    
    # 检查服务商品必须关联模板配置
    local invalid_service_goods=$(execute_sql "SELECT COUNT(*) FROM ls_goods WHERE goods_type = 2 AND template_config_id IS NULL;" 2>/dev/null | tail -1)
    if [ "$invalid_service_goods" = "0" ]; then
        log_success "服务商品数据一致性检查通过"
    else
        log_error "发现 $invalid_service_goods 个服务商品未关联模板配置"
    fi
    
    # 检查团购活动最小人数
    local invalid_activities=$(execute_sql "SELECT COUNT(*) FROM ls_group_buy_activities WHERE min_participants <= 0;" 2>/dev/null | tail -1)
    if [ "$invalid_activities" = "0" ]; then
        log_success "团购活动数据一致性检查通过"
    else
        log_error "发现 $invalid_activities 个团购活动最小人数设置无效"
    fi
    
    # 检查参数会话过期时间
    local invalid_sessions=$(execute_sql "SELECT COUNT(*) FROM ls_parameter_fill_sessions WHERE expires_at < created_at;" 2>/dev/null | tail -1)
    if [ "$invalid_sessions" = "0" ]; then
        log_success "参数会话数据一致性检查通过"
    else
        log_error "发现 $invalid_sessions 个参数会话过期时间设置异常"
    fi
}

# 验证关键配置
validate_key_configurations() {
    log_info "验证关键配置..."
    
    # 检查是否有服务商品
    local service_goods_count=$(execute_sql "SELECT COUNT(*) FROM ls_goods WHERE goods_type = 2;" 2>/dev/null | tail -1)
    if [ "$service_goods_count" -gt 0 ]; then
        log_success "系统中存在 $service_goods_count 个服务商品"
    else
        log_warning "系统中暂无服务商品，建议创建测试数据"
    fi
    
    # 检查是否有团购活动
    local group_buy_count=$(execute_sql "SELECT COUNT(*) FROM ls_group_buy_activities;" 2>/dev/null | tail -1)
    if [ "$group_buy_count" -gt 0 ]; then
        log_success "系统中存在 $group_buy_count 个团购活动"
    else
        log_warning "系统中暂无团购活动"
    fi
    
    # 检查参数填写会话
    local session_count=$(execute_sql "SELECT COUNT(*) FROM ls_parameter_fill_sessions;" 2>/dev/null | tail -1)
    if [ "$session_count" -gt 0 ]; then
        log_success "系统中存在 $session_count 个参数填写会话"
    else
        log_warning "系统中暂无参数填写会话"
    fi
}

# 生成详细报告
generate_detailed_report() {
    log_info "生成详细验证报告..."
    
    echo ""
    echo "=========================================="
    echo "数据库结构详细信息"
    echo "=========================================="
    
    # 商品表扩展字段详情
    echo ""
    echo "【商品表扩展字段】"
    execute_sql "
        SELECT 
            COLUMN_NAME as '字段名',
            DATA_TYPE as '数据类型',
            IS_NULLABLE as '可为空',
            COLUMN_DEFAULT as '默认值',
            COLUMN_COMMENT as '字段注释'
        FROM information_schema.COLUMNS 
        WHERE table_schema = DATABASE() 
        AND table_name = 'ls_goods' 
        AND COLUMN_NAME IN (
            'goods_type', 'template_config_id', 'service_delivery_time',
            'print_materials', 'print_sizes', 'support_group_buy',
            'group_buy_min_count', 'group_buy_price', 'group_buy_duration'
        )
        ORDER BY ORDINAL_POSITION;
    " 2>/dev/null
    
    # 新表信息
    echo ""
    echo "【新创建的表】"
    execute_sql "
        SELECT 
            table_name as '表名',
            table_comment as '表注释'
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name IN (
            'ls_parameter_fill_sessions',
            'ls_template_order_params', 
            'ls_service_delivery_records',
            'ls_group_buy_activities',
            'ls_group_buy_participants',
            'ls_file_download_logs'
        )
        ORDER BY table_name;
    " 2>/dev/null
}

# 生成验证报告
generate_report() {
    local pass_rate=0
    if [ "$TOTAL_CHECKS" -gt 0 ]; then
        pass_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    fi
    
    echo ""
    echo "=========================================="
    echo "模板商品化系统数据库验证报告"
    echo "=========================================="
    echo "验证时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过检查: $PASSED_CHECKS"
    echo "失败检查: $FAILED_CHECKS"
    echo "通过率: $pass_rate%"
    echo "=========================================="
    
    if [ "$pass_rate" -ge 95 ]; then
        echo -e "${GREEN}✓ 验证结果: 优秀 - 数据库结构完整，可以正常使用${NC}"
    elif [ "$pass_rate" -ge 80 ]; then
        echo -e "${YELLOW}⚠ 验证结果: 良好 - 存在少量问题，建议修复后使用${NC}"
    elif [ "$pass_rate" -ge 60 ]; then
        echo -e "${YELLOW}⚠ 验证结果: 一般 - 存在较多问题，需要修复后使用${NC}"
    else
        echo -e "${RED}✗ 验证结果: 不合格 - 存在严重问题，不建议使用${NC}"
    fi
    
    echo "=========================================="
}

# 主执行流程
main() {
    echo "模板商品化系统数据库结构验证脚本"
    echo "========================================"
    echo ""
    
    # 检查数据库连接
    check_database_connection
    echo ""
    
    # 执行各项验证
    validate_goods_table_extension
    echo ""
    
    validate_new_tables
    echo ""
    
    validate_data_integrity
    echo ""
    
    validate_key_configurations
    echo ""
    
    # 生成详细报告（可选）
    if [ "$1" = "--detailed" ] || [ "$1" = "-d" ]; then
        generate_detailed_report
    fi
    
    # 生成验证报告
    generate_report
    
    # 返回适当的退出码
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -d, --detailed  显示详细报告"
    echo ""
    echo "配置数据库连接:"
    echo "  请编辑脚本开头的数据库配置变量"
    echo "  DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASS"
    echo ""
    exit 0
fi

# 执行主程序
main "$@"
