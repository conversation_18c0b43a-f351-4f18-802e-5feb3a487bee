# 模板商品化系统快速功能验证清单

## 验证概述
本清单提供模板商品化系统核心功能的快速验证步骤，适用于快速确认系统功能完整性。

## 前置检查 (5分钟)

### ✅ 环境检查
- [ ] PHP 7.4+ 运行正常
- [ ] 数据库连接正常
- [ ] Web服务器访问正常
- [ ] 缓存已清理

### ✅ 数据库结构检查
使用自动化验证脚本：
```bash
# 快速验证（推荐）
php docs/database_validation_script.php

# 或使用Shell脚本
bash docs/database_validation.sh
```

**检查点**:
- [ ] 脚本执行成功，无错误
- [ ] 通过率 ≥ 95%
- [ ] 商品表扩展字段：9个字段全部存在
- [ ] 新核心表：6个表全部创建
- [ ] 数据完整性检查通过

## 核心功能验证 (30分钟)

### 1. 商品管理扩展 (5分钟)

#### ✅ 服务商品创建
**路径**: 后台 → 商品管理 → 添加商品
**步骤**:
1. 选择"服务商品"类型
2. 填写基本信息：
   - 商品名称: "测试海报服务"
   - 模板配置: 选择任一可用模板
   - 支持团购: 开启
   - 团购价格: 19.90
   - 团购最小人数: 3
3. 保存商品

**验证点**: 
- [ ] 服务商品配置区域正常显示
- [ ] 模板配置下拉框有选项
- [ ] 团购设置保存成功
- [ ] 商品列表显示"服务商品"标识

### 2. 参数填写会话管理 (5分钟)

#### ✅ API接口测试
使用Postman或curl测试：

**创建会话**:
```bash
POST /api/parameter-session/create
{
  "goods_id": [刚创建的服务商品ID]
}
```

**更新参数**:
```bash
PUT /api/parameter-session/{session_id}
{
  "parameter_values": {
    "title": "测试标题",
    "subtitle": "测试副标题"
  }
}
```

**验证点**:
- [ ] 会话创建成功，返回session_id
- [ ] 参数更新成功，完成度计算正确
- [ ] 后台参数会话管理页面显示记录

### 3. 团购功能 (10分钟)

#### ✅ 团购活动创建
**路径**: 后台 → 团购管理 → 添加团购活动
**步骤**:
1. 选择刚创建的服务商品
2. 填写活动信息：
   - 活动标题: "测试团购活动"
   - 团购价格: 19.90
   - 最小成团人数: 3
   - 活动时长: 24小时
3. 创建活动

**验证点**:
- [ ] 团购活动创建成功
- [ ] 活动列表显示新活动
- [ ] 活动状态为"进行中"

#### ✅ 团购参与测试
使用API测试用户参与：
```bash
POST /api/group-buy/participate
{
  "activity_id": "[团购活动ID]",
  "session_id": "[参数会话ID]",
  "quantity": 1
}
```

**验证点**:
- [ ] 参与成功
- [ ] 团购进度更新 (1/3)
- [ ] 后台可查看参与记录

### 4. 服务交付管理 (5分钟)

#### ✅ 交付记录创建
**模拟步骤**:
1. 创建一个服务订单（可通过数据库直接插入测试数据）
2. 访问后台服务交付管理

**验证点**:
- [ ] 生成任务管理页面正常访问
- [ ] 打印管理页面正常访问  
- [ ] 物流管理页面正常访问
- [ ] 任务列表显示正常

### 5. 订单管理扩展 (5分钟)

#### ✅ 服务订单API
测试服务订单创建：
```bash
POST /api/service-order/create
{
  "session_id": "[参数会话ID]",
  "address_id": 1,
  "print_options": {
    "material": "高光相纸",
    "size": "A4"
  }
}
```

**验证点**:
- [ ] 服务订单创建成功
- [ ] 订单类型为服务订单
- [ ] 后台订单列表显示服务订单标识

## 业务流程验证 (15分钟)

### ✅ 单人购买流程
**完整流程测试**:
1. 创建参数填写会话 ✓
2. 填写参数值 ✓
3. 生成预览 ✓
4. 创建服务订单 ✓
5. 模拟支付成功 ✓
6. 检查交付记录创建 ✓

### ✅ 团购流程
**简化流程测试**:
1. 创建团购活动 ✓
2. 用户参与团购 ✓
3. 检查团购进度 ✓
4. 模拟达到成团条件 ✓
5. 验证自动成团 ✓

## 后台管理页面验证 (10分钟)

### ✅ 页面访问检查
逐一访问以下页面，确认正常显示：

**商品管理**:
- [ ] `/admin/goods/lists` - 商品列表
- [ ] `/admin/goods/add` - 添加商品

**团购管理**:
- [ ] `/admin/group_buy/lists` - 团购活动列表
- [ ] `/admin/group_buy/add` - 创建团购活动

**服务交付管理**:
- [ ] `/admin/service_delivery/generation_tasks` - 生成任务管理
- [ ] `/admin/service_delivery/print_management` - 打印管理
- [ ] `/admin/service_delivery/logistics` - 物流管理

**参数会话管理**:
- [ ] `/admin/parameter_session/lists` - 参数会话列表

### ✅ 功能操作检查
- [ ] 数据列表正常显示
- [ ] 筛选功能正常工作
- [ ] 分页功能正常
- [ ] 操作按钮响应正常

## API接口验证 (10分钟)

### ✅ 核心API接口测试
使用API测试工具验证以下接口：

**团购相关**:
- [ ] `GET /api/group-buy/list` - 团购列表
- [ ] `GET /api/group-buy/{id}` - 团购详情
- [ ] `POST /api/group-buy/participate` - 参与团购

**参数会话相关**:
- [ ] `POST /api/parameter-session/create` - 创建会话
- [ ] `PUT /api/parameter-session/{id}` - 更新参数
- [ ] `GET /api/parameter-session/{id}` - 获取详情

**服务订单相关**:
- [ ] `POST /api/service-order/create` - 创建订单
- [ ] `GET /api/service-order/{id}/status` - 查询状态

### ✅ 接口响应检查
- [ ] 所有接口返回正确的JSON格式
- [ ] 成功响应包含必要数据字段
- [ ] 错误响应包含明确错误信息
- [ ] 响应时间在2秒内

## 验证结果汇总

### 通过标准
- [ ] 所有核心功能正常工作
- [ ] 所有后台页面正常访问
- [ ] 所有API接口正常响应
- [ ] 业务流程完整可用

### 验证完成确认
**验证人员**: ________________  
**验证时间**: ________________  
**验证结果**: ☐ 通过 ☐ 部分通过 ☐ 未通过  

**问题记录**:
```
[记录发现的问题和建议修复方案]
```

**总体评估**:
```
[系统整体功能完整性评估]
```

## 快速问题排查

### 常见问题及解决方案

**问题1**: 商品类型选择器不显示
- 检查JavaScript文件是否正确加载
- 检查浏览器控制台是否有错误

**问题2**: API接口返回404
- 检查路由配置是否正确
- 检查控制器文件是否存在

**问题3**: 数据库字段不存在
- 检查数据库迁移是否执行完成
- 运行DIRECT_CHECK.sql验证数据库状态

**问题4**: 页面显示异常
- 清理缓存：`rm -rf runtime/cache/*`
- 检查模板文件是否存在

**问题5**: 权限相关错误
- 检查目录权限设置
- 确认管理员权限配置正确

---

**注意**: 此快速验证清单适用于开发和测试环境的功能确认。生产环境部署前请执行完整的验证指南。
